import CryptoJS from 'crypto-js';
// import store from './store';

const sKey = CryptoJS.enc.Utf8.parse('xFjVmBUKEKSYNjUe');

export function EncryptAES(s: string): string {
    // key 和 iv 使用同一个值
    const encrypted = CryptoJS.AES.encrypt(s, sKey, {
        iv: sKey,
        mode: CryptoJS.mode.CBC, // CBC算法
        padding: CryptoJS.pad.Pkcs7, //使用pkcs7 进行padding 后端需要注意
    });

    return encrypted.toString();
}

export function DecryptAES(s: string): string {
    // key 和 iv 使用同一个值
    const decrypted = CryptoJS.AES.decrypt(s, sKey, {
        iv: sKey,
        mode: CryptoJS.mode.CBC, // CBC算法
        padding: CryptoJS.pad.Pkcs7, //使用pkcs7 进行padding 后端需要注意
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
}


// 接口报错统一提示
function errorCallBack(message) {
    console.error(`${message || "接口传参错误"}`); // 正式使用时使用$message提示
}

type requestOption = {
    config: string
    timeout: number,
    type: string
}

// 公共逻辑处理
async function request(method, path, data, config = {}, type = "json", timeout = 20000) {
    //@ts-ignore
    if (!(typeof method === "string" && ["get", "post", "delete", "put"].includes(method)) || !(typeof path === "string") || !(!data || typeof data === "object") || !(typeof config === "object") || !(typeof type === "string")) {
        errorCallBack("");
        return;
    }
    const configDefault = {headers: {authorization: ""}};
    method = method.toUpperCase();
    let baseUrl = localStorage.getItem('baseUrl') || "http://10.0.0.7:20003"
    let initConfig;
    let url = path.startsWith("http") ? path : baseUrl + '/api/user/' + path;
    const token = localStorage.getItem("token")
    if (token) configDefault.headers.authorization = "Bearer " + token; // 这里的authorization怎么赋值要看服务端的设置
    if (method === "get") {
        initConfig = {...configDefault, ...config};
        const params = data
            ? JSON.stringify(data)
                .replace(/:/g, "=")
                .replace(/"/g, "")
                .replace(/,/g, "&")
                .match(/\{([^)]*)\}/)[1]
            : ""; // 有问题就使用qs开源库处理
        if (params) url += `${url.includes("?") ? "&" : "?"}${params}`;
    } else {
        // @ts-ignore
        const body = data && ["json", "text", "blob"].includes(type) ? JSON.stringify(data) : data; // json file text
        initConfig = data ? {...configDefault, ...config, method, body} : {...configDefault, ...config, method};
    }
    // @ts-ignore
    if (["json", "text", "blob"].includes(type)) initConfig.headers["Content-Type"] = "application/json";

    const response = await fetch(url, initConfig);
    // response.status [200,300)
    if (response.ok) {
        // @ts-ignore
        if (["text"].includes(type)) return response.text();
        // @ts-ignore
        if (["blob"].includes(type)) return response.blob();
        // @ts-ignore
        if (["arrayBuffer"].includes(type)) return response.arrayBuffer();
        return response.json();
    }
    // 也可以塞入对token失效等情况的判断以及处理逻辑
    errorCallBack("Something went wrong on API server!");
    throw new Error("Something went wrong on API server!");
}

function encryptParams(params) {
    // return params;
    let encryptParams = {};
    for (let key in params) {
        encryptParams[EncryptAES(key)] = EncryptAES('' + params[key]);
    }
    return encryptParams
}

function getSign(params) {
    if (typeof params == "string") {
        return paramsStrSort(params);
    } else if (typeof params == "object") {
        var arr = [];
        for (var i in params) {
            arr.push((i + "=" + params[i]));
        }
        return paramsStrSort(arr.join(("&")));
    }
}

function paramsStrSort(paramsStr) {
    var urlStr = paramsStr.split("&").sort().join("&");
    return CryptoJS.SHA256(CryptoJS.MD5(urlStr).toString(CryptoJS.enc.Hex)).toString(CryptoJS.enc.Hex);
}

export function get(path, params, options: requestOption = {config: null, type: null, timeout: null}) {
    const config = options.config || {};
    const type = options.type || "json";
    const timeout = options.timeout || 20000
    params.t = Date.now();
    params.sign = getSign(params);
    console.log(path, params)
    return request("get", path, encryptParams(params), config, type, timeout);
}


export function post(path, params, options: requestOption = {config: null, type: null, timeout: null}) {
    const config = options.config || {};
    const type = options.type || "json";
    const timeout = options.timeout || 20000
    params.t = Date.now();
    params.sign = getSign(params);
    console.log(path, params)
    return request("post", path, encryptParams(params), config, type, timeout);
}

// 不加密的post
export function postOrigin(path, params, options: requestOption = {config: null, type: null, timeout: null}) {
    const config = options.config || {};
    const type = options.type || "json";
    const timeout = options.timeout || 20000
    console.log(path, params)
    return request("post", path, params, config, type, timeout);
}


// 使用
// import {post} from "db://assets/utils/request";
// post("info", {music: "name"}).then(res => {
//     console.log(res)
// })
// {"0iwUh4UQVBRvH7WC2AYnwQ==":"N4KjIxfDB9RVZuegweAK8A=="}
