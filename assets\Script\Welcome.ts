import {
  _decorator,
  Component,
  Node,
  director,
  tween,
  Vec3,
  Label,
  Widget,
  v3,
  <PERSON>ua<PERSON>,
  UIOpacity,
} from "cc"
import store from "./utils/store"
import { changeToken } from "../api/user"
import { Toast } from "./utils/Toast"
import { getBrowserValue } from "./utils/Helper"
import { Dialog } from "./Dialog"
import { ResourceManager } from "db://assets/Script/utils/ResourceManager"
import { EventEnum } from "./utils/Enum"
import EventManager from "./utils/EventManager"

const { ccclass, property } = _decorator

@ccclass("Welcome")
export default class Welcome extends Component {
  @property(Dialog) ruleDialog: Dialog
  @property(Node) ruleLabel: Node
  @property(Node) uidLabel: Node
  @property(Node) bg: Node
  @property(Node) title: Node
  @property(Node) dragon: Node
  @property(Node) button: Node
  @property(Node) cloud: Node

  private isReadRule: boolean = false

  async start() {
    this.login()
    this.createAnimation()
    const bgWidget = this.bg.getComponent(Widget)
    const titleWidget = this.title.getComponent(Widget)
    const dragonWidget = this.dragon.getComponent(Widget)
    const buttonWidget = this.button.getComponent(Widget)

    const availHeight = document.getElementsByTagName("body")[0].offsetHeight
    console.log("height", availHeight)

    if (availHeight < 620) {
      titleWidget.top -= 30
      dragonWidget.top -= 50
      buttonWidget.top -= 50
    } else if (availHeight < 720) {
      console.log("开始适配小屏幕")
    } else if (availHeight > 800) {
      titleWidget.top += 30
      dragonWidget.top += 50
      buttonWidget.top += 50
    } else {
    }
    await Promise.all([ResourceManager.Instance.loadAllPrefabRes()])

    director.preloadScene("main")

    const lotteryJumpAdGameLogId = localStorage.getItem(
      "lotteryJumpAdGameLogId"
    )
    const lotteryJumpAdAt = localStorage.getItem("lotteryJumpAdAt")
    const token = localStorage.getItem("token")
    if (lotteryJumpAdGameLogId && lotteryJumpAdAt && token) {
      if (Date.now() - Number(lotteryJumpAdAt) < 10 * 60 * 1000) {
        // console.log("跳转广告页")
        // console.log("localStorage", lotteryJumpAdGameLogId, lotteryJumpAdAt, lotteryJumpAdScore, token)
        // 十分钟之内转跳出去
        store.setGameLogId(Number.parseInt(lotteryJumpAdGameLogId))
        store.setToken(token)
        localStorage.removeItem("lotteryJumpAdGameLogId")
        localStorage.removeItem("lotteryJumpAdAt")
        store.setDirectlyLottery(true)
        setTimeout(() => {
          director.loadScene("main")
        }, 300)
        return
      }
    }

    EventManager.Instance.on(
      EventEnum.StartGame,
      () => {
        this.onClickStart()
      },
      this
    )
  }

  onClickStart() {
    if (!this.isReadRule) {
      this.showRuleDialog()
    } else {
      director.loadScene("main")
    }
  }

  showRuleDialog() {
    this.ruleDialog.showDialog()
    this.isReadRule = true
  }

  login() {
    const short_token = getBrowserValue("short_token")
    changeToken({
      short_token: short_token,
    })
      .then((res) => {
        if (res.code === 200) {
          let token = res.result.token
          store.setToken(token)
          store.fetchInfo().then(() => {
            console.log("fetchInfo success !")
            const uid = store.userId
            const uidLabel = this.uidLabel.getComponent(Label)
            uidLabel.string = `UID: ${uid}`
          })
        } else {
          Toast.show(res.message, 3)
        }
      })
      .catch(() => {
        Toast.show("网络繁忙，请稍后再试", 3)
        console.log("changeToken fail !")
      })
  }

  createAnimation() {
    const button = this.node.getChildByName("Button")
    console.log("button", button)
    if (button) {
      tween(button)
        .to(0.5, { scale: new Vec3(1.05, 1.05, 1.05) })
        .to(0.5, { scale: new Vec3(1, 1, 1) })
        .union()
        .repeatForever()
        .start()
    }

    let titleQuatStart: Quat = new Quat()
    Quat.fromEuler(titleQuatStart, 0, 0, 0.5)

    let titleQuatEnd: Quat = new Quat()
    Quat.fromEuler(titleQuatEnd, 0, 0, -0.5)

    if (this.title) {
      tween(this.title)
        .to(1, { rotation: titleQuatStart })
        .to(1, { rotation: titleQuatEnd })
        .union()
        .repeatForever()
        .start()
    }

    // if (this.dragon) {
    //   tween(this.dragon)
    //     .to(1.2, {
    //       position: new Vec3(this.dragon.position.x, this.dragon.position.y, 0),
    //     })
    //     .to(1.2, {
    //       position: new Vec3(
    //         this.dragon.position.x,
    //         this.dragon.position.y - 20,
    //         0
    //       ),
    //     })
    //     .union()
    //     .repeatForever()
    //     .start()
    // }

    if (this.cloud) {
      tween(this.cloud)
        .to(2.8, { position: new Vec3(0, this.cloud.position.y, 0) })
        .to(2.8, { position: new Vec3(0, this.cloud.position.y - 30, 0) })
        .union()
        .repeatForever()
        .start()
    }
  }
}
