import {_decorator, Component, Node, Button, Event, Label, Layout} from "cc"
import store from "./utils/store"

const {ccclass, property} = _decorator
import {Dialog} from "./Dialog"
import {EventEnum} from "./utils/Enum"
import EventManager from "./utils/EventManager"

@ccclass("WinningAlert")
export class WinningAlert extends Dialog {
    @property(Button) knowMoreBtn: Button
    @property(Button) tryAgainBtn: Button
    @property(Label) prizeLabel: Label

    private prize: string = null

    start() {
        super.start()

        const {open_ad} = store.config

        if (open_ad) {
            this.knowMoreBtn.node.active = true
            this.knowMoreBtn.node.on(Button.EventType.CLICK, () => {
                console.log("knowMore")
                EventManager.Instance.emit(EventEnum.KnowMore)
            })
        } else {
            this.node.getComponentInChildren(Layout).paddingLeft = 242
            this.knowMoreBtn.node.active = false
        }

        this.tryAgainBtn.node.on(Button.EventType.CLICK, () => {
            console.log("tryAgain")
            this.hideDialog()
            EventManager.Instance.emit(EventEnum.TryAgain)
        })
    }

    update(deltaTime: number) {
        super.update(deltaTime)
    }

    setPrize(prize: string) {
        this.prize = prize
        this.prizeLabel.string = this.prize
    }

    showDialog(x: number = 0, y: number = 0) {
        super.showDialog(x, y)
    }

    hideDialog() {
        super.hideDialog()
    }
}
