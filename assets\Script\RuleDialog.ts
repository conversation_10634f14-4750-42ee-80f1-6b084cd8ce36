import { _decorator, Event, Node, Button, Label } from "cc"

const { ccclass, property } = _decorator
import { Dialog } from "./Dialog"
import EventManager from "./utils/EventManager"
import { EventEnum } from "./utils/Enum"

@ccclass("RuleDialog")
export class RuleDialog extends Dialog {
  @property(Button) closeBtn: Button
  @property(Button) confirmBtn: Button

  start() {
    super.start()

    this.closeBtn.node.on(Button.EventType.CLICK, () => {
      this.hideDialog()
    })

    this.confirmBtn.node.on(Button.EventType.CLICK, () => {
      this.hideDialog()
      EventManager.Instance.emit(EventEnum.StartGame)
    })
  }

  update(deltaTime: number) {
    super.update(deltaTime)
  }

  showDialog(x: number = 0, y: number = 0) {
    super.showDialog(x, y)
  }
}
