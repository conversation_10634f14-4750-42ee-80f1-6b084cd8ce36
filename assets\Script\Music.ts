import {
  _decorator,
  AudioSource,
  CCFloat,
  Component,
  Button,
  Sprite,
  Sprite<PERSON>rame,
} from "cc"
import { AudioMgr } from "./AudioMgr"

const { ccclass, property } = _decorator

@ccclass("Music")
export class Music extends Component {
  @property(AudioSource) bgm: AudioSource
  @property(Sprite) iconNode: Sprite
  @property(SpriteFrame) playImg: SpriteFrame
  @property(SpriteFrame) stopImg: SpriteFrame
  @property(CCFloat) rotateSpeed = 10

  start() {
    const isMute = AudioMgr.inst.audioSource.volume <= 0
    const isPlaying = AudioMgr.inst.audioSource.playing
    console.log("isMute============", isMute)
    console.log("isPlaying============", isPlaying)
    if (isPlaying) {
      this.iconNode.spriteFrame = isMute ? this.stopImg : this.playImg
    } else if (isMute) {
      this.iconNode.spriteFrame = this.stopImg
    } else {
      setTimeout(() => {
        AudioMgr.inst.play(this.bgm.clip, 0.5, true)
        this.iconNode.spriteFrame = this.playImg
      }, 300)
    }

    this.node.on(Button.EventType.CLICK, () => {
      const isMute = AudioMgr.inst.audioSource.volume <= 0
      const isPlaying = AudioMgr.inst.audioSource.playing
      if (isPlaying && !isMute) {
        AudioMgr.inst.pause()
        this.iconNode.spriteFrame = this.stopImg
        this.iconNode.node.angle = 0
      } else {
        AudioMgr.inst.resume()
        this.iconNode.spriteFrame = this.playImg
      }
    })
  }

  update(deltaTime: number) {
    const isPlaying = AudioMgr.inst.audioSource.playing && AudioMgr.inst.audioSource.volume > 0
    if (isPlaying) {
      this.iconNode.node.angle -= this.rotateSpeed
      this.iconNode.spriteFrame = this.playImg
    }else{
      this.iconNode.spriteFrame = this.stopImg
    }
  }
}
