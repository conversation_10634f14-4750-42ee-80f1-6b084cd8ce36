[{"__type__": "cc.Prefab", "_name": "Item9", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item9", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8c1a2cd4-228f-4611-9751-d80ee0533801@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": 0.5, "y": 66.5}, {"__type__": "cc.Vec2", "x": -5.5, "y": 64.5}, {"__type__": "cc.Vec2", "x": -10.5, "y": 59.5}, {"__type__": "cc.Vec2", "x": -16.5, "y": 62.5}, {"__type__": "cc.Vec2", "x": -30.5, "y": 61.5}, {"__type__": "cc.Vec2", "x": -37.5, "y": 55.5}, {"__type__": "cc.Vec2", "x": -40.5, "y": 49.5}, {"__type__": "cc.Vec2", "x": -40.5, "y": 39.5}, {"__type__": "cc.Vec2", "x": -36.5, "y": 31.5}, {"__type__": "cc.Vec2", "x": -28.5, "y": 26.5}, {"__type__": "cc.Vec2", "x": -23.5, "y": 26.5}, {"__type__": "cc.Vec2", "x": -19.5, "y": 22.5}, {"__type__": "cc.Vec2", "x": -19.5, "y": 20.5}, {"__type__": "cc.Vec2", "x": -16.5, "y": 17.5}, {"__type__": "cc.Vec2", "x": -16.5, "y": 10.5}, {"__type__": "cc.Vec2", "x": -14.5, "y": 7.5}, {"__type__": "cc.Vec2", "x": -11.5, "y": 7.5}, {"__type__": "cc.Vec2", "x": -8.5, "y": 12.5}, {"__type__": "cc.Vec2", "x": -6.5, "y": 12.5}, {"__type__": "cc.Vec2", "x": -6.5, "y": 10.5}, {"__type__": "cc.Vec2", "x": -4.5, "y": 9.5}, {"__type__": "cc.Vec2", "x": -2.5, "y": 3.5}, {"__type__": "cc.Vec2", "x": -0.5, "y": 2.5}, {"__type__": "cc.Vec2", "x": 0.5, "y": -1.5}, {"__type__": "cc.Vec2", "x": 2.5, "y": -2.5}, {"__type__": "cc.Vec2", "x": 3.5, "y": -6.5}, {"__type__": "cc.Vec2", "x": 5.5, "y": -7.5}, {"__type__": "cc.Vec2", "x": 6.5, "y": -11.5}, {"__type__": "cc.Vec2", "x": 8.5, "y": -12.5}, {"__type__": "cc.Vec2", "x": 10.5, "y": -18.5}, {"__type__": "cc.Vec2", "x": 12.5, "y": -19.5}, {"__type__": "cc.Vec2", "x": 13.5, "y": -23.5}, {"__type__": "cc.Vec2", "x": 17.5, "y": -28.5}, {"__type__": "cc.Vec2", "x": 24.5, "y": -64.5}, {"__type__": "cc.Vec2", "x": 27.5, "y": -66.5}, {"__type__": "cc.Vec2", "x": 31.5, "y": -66.5}, {"__type__": "cc.Vec2", "x": 33.5, "y": -64.5}, {"__type__": "cc.Vec2", "x": 32.5, "y": -54.5}, {"__type__": "cc.Vec2", "x": 34.5, "y": -56.5}, {"__type__": "cc.Vec2", "x": 39.5, "y": -55.5}, {"__type__": "cc.Vec2", "x": 40.5, "y": -47.5}, {"__type__": "cc.Vec2", "x": 38.5, "y": -46.5}, {"__type__": "cc.Vec2", "x": 37.5, "y": -42.5}, {"__type__": "cc.Vec2", "x": 35.5, "y": -41.5}, {"__type__": "cc.Vec2", "x": 34.5, "y": -37.5}, {"__type__": "cc.Vec2", "x": 32.5, "y": -36.5}, {"__type__": "cc.Vec2", "x": 30.5, "y": -30.5}, {"__type__": "cc.Vec2", "x": 28.5, "y": -29.5}, {"__type__": "cc.Vec2", "x": 25.5, "y": -23.5}, {"__type__": "cc.Vec2", "x": 25.5, "y": -18.5}, {"__type__": "cc.Vec2", "x": 18.5, "y": 9.5}, {"__type__": "cc.Vec2", "x": 18.5, "y": 12.5}, {"__type__": "cc.Vec2", "x": 21.5, "y": 14.5}, {"__type__": "cc.Vec2", "x": 23.5, "y": 18.5}, {"__type__": "cc.Vec2", "x": 28.5, "y": 18.5}, {"__type__": "cc.Vec2", "x": 27.5, "y": 22.5}, {"__type__": "cc.Vec2", "x": 22.5, "y": 26.5}, {"__type__": "cc.Vec2", "x": 18.5, "y": 35.5}, {"__type__": "cc.Vec2", "x": 22.5, "y": 43.5}, {"__type__": "cc.Vec2", "x": 22.5, "y": 51.5}, {"__type__": "cc.Vec2", "x": 20.5, "y": 57.5}, {"__type__": "cc.Vec2", "x": 14.5, "y": 63.5}, {"__type__": "cc.Vec2", "x": 7.5, "y": 66.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7vsJKCYVJMIu2iV2NFcvv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]