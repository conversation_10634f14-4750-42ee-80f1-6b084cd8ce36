[{"__type__": "cc.Prefab", "_name": "Item7", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item7", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 312, "height": 190}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "daeea92d-8968-43df-b0b7-987e1cd0b6f9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": 36, "y": 95}, {"__type__": "cc.Vec2", "x": 33, "y": 89}, {"__type__": "cc.Vec2", "x": 33, "y": 84}, {"__type__": "cc.Vec2", "x": 35, "y": 77}, {"__type__": "cc.Vec2", "x": 38, "y": 71}, {"__type__": "cc.Vec2", "x": 40, "y": 70}, {"__type__": "cc.Vec2", "x": 40, "y": 47}, {"__type__": "cc.Vec2", "x": 42, "y": 41}, {"__type__": "cc.Vec2", "x": 51, "y": 31}, {"__type__": "cc.Vec2", "x": 44, "y": 32}, {"__type__": "cc.Vec2", "x": 41, "y": 36}, {"__type__": "cc.Vec2", "x": 38, "y": 37}, {"__type__": "cc.Vec2", "x": 42, "y": 25}, {"__type__": "cc.Vec2", "x": 49, "y": 18}, {"__type__": "cc.Vec2", "x": 46, "y": 5}, {"__type__": "cc.Vec2", "x": 46, "y": -32}, {"__type__": "cc.Vec2", "x": 42, "y": -41}, {"__type__": "cc.Vec2", "x": 34, "y": -48}, {"__type__": "cc.Vec2", "x": 28, "y": -51}, {"__type__": "cc.Vec2", "x": 7, "y": -56}, {"__type__": "cc.Vec2", "x": -72, "y": -54}, {"__type__": "cc.Vec2", "x": -80, "y": -48}, {"__type__": "cc.Vec2", "x": -83, "y": -38}, {"__type__": "cc.Vec2", "x": -90, "y": -24}, {"__type__": "cc.Vec2", "x": -97, "y": -16}, {"__type__": "cc.Vec2", "x": -97, "y": -14}, {"__type__": "cc.Vec2", "x": -103, "y": -9}, {"__type__": "cc.Vec2", "x": -101, "y": -5}, {"__type__": "cc.Vec2", "x": -101, "y": 6}, {"__type__": "cc.Vec2", "x": -108, "y": 17}, {"__type__": "cc.Vec2", "x": -108, "y": 26}, {"__type__": "cc.Vec2", "x": -110, "y": 26}, {"__type__": "cc.Vec2", "x": -117, "y": 17}, {"__type__": "cc.Vec2", "x": -119, "y": 21}, {"__type__": "cc.Vec2", "x": -119, "y": 27}, {"__type__": "cc.Vec2", "x": -122, "y": 31}, {"__type__": "cc.Vec2", "x": -126, "y": 33}, {"__type__": "cc.Vec2", "x": -126, "y": 24}, {"__type__": "cc.Vec2", "x": -128, "y": 22}, {"__type__": "cc.Vec2", "x": -131, "y": 26}, {"__type__": "cc.Vec2", "x": -142, "y": 29}, {"__type__": "cc.Vec2", "x": -146, "y": 33}, {"__type__": "cc.Vec2", "x": -148, "y": 39}, {"__type__": "cc.Vec2", "x": -152, "y": 31}, {"__type__": "cc.Vec2", "x": -153, "y": 21}, {"__type__": "cc.Vec2", "x": -150, "y": 12}, {"__type__": "cc.Vec2", "x": -147, "y": 10}, {"__type__": "cc.Vec2", "x": -145, "y": 6}, {"__type__": "cc.Vec2", "x": -148, "y": 8}, {"__type__": "cc.Vec2", "x": -153, "y": 8}, {"__type__": "cc.Vec2", "x": -156, "y": 12}, {"__type__": "cc.Vec2", "x": -155, "y": 3}, {"__type__": "cc.Vec2", "x": -149, "y": -3}, {"__type__": "cc.Vec2", "x": -142, "y": -6}, {"__type__": "cc.Vec2", "x": -138, "y": -6}, {"__type__": "cc.Vec2", "x": -140, "y": -8}, {"__type__": "cc.Vec2", "x": -150, "y": -7}, {"__type__": "cc.Vec2", "x": -153, "y": -9}, {"__type__": "cc.Vec2", "x": -153, "y": -11}, {"__type__": "cc.Vec2", "x": -145, "y": -12}, {"__type__": "cc.Vec2", "x": -142, "y": -14}, {"__type__": "cc.Vec2", "x": -142, "y": -16}, {"__type__": "cc.Vec2", "x": -135, "y": -19}, {"__type__": "cc.Vec2", "x": -118, "y": -20}, {"__type__": "cc.Vec2", "x": -116, "y": -39}, {"__type__": "cc.Vec2", "x": -117, "y": -54}, {"__type__": "cc.Vec2", "x": -115, "y": -63}, {"__type__": "cc.Vec2", "x": -110, "y": -72}, {"__type__": "cc.Vec2", "x": -103, "y": -79}, {"__type__": "cc.Vec2", "x": -87, "y": -88}, {"__type__": "cc.Vec2", "x": -61, "y": -94}, {"__type__": "cc.Vec2", "x": 45, "y": -95}, {"__type__": "cc.Vec2", "x": 65, "y": -93}, {"__type__": "cc.Vec2", "x": 90, "y": -86}, {"__type__": "cc.Vec2", "x": 102, "y": -76}, {"__type__": "cc.Vec2", "x": 108, "y": -65}, {"__type__": "cc.Vec2", "x": 110, "y": -57}, {"__type__": "cc.Vec2", "x": 110, "y": -44}, {"__type__": "cc.Vec2", "x": 108, "y": -35}, {"__type__": "cc.Vec2", "x": 105, "y": -26}, {"__type__": "cc.Vec2", "x": 95, "y": -8}, {"__type__": "cc.Vec2", "x": 99, "y": -6}, {"__type__": "cc.Vec2", "x": 108, "y": -5}, {"__type__": "cc.Vec2", "x": 112, "y": -1}, {"__type__": "cc.Vec2", "x": 114, "y": -1}, {"__type__": "cc.Vec2", "x": 113, "y": -7}, {"__type__": "cc.Vec2", "x": 120, "y": -14}, {"__type__": "cc.Vec2", "x": 120, "y": -4}, {"__type__": "cc.Vec2", "x": 124, "y": -1}, {"__type__": "cc.Vec2", "x": 123, "y": -9}, {"__type__": "cc.Vec2", "x": 124, "y": -13}, {"__type__": "cc.Vec2", "x": 127, "y": -16}, {"__type__": "cc.Vec2", "x": 127, "y": -21}, {"__type__": "cc.Vec2", "x": 129, "y": -24}, {"__type__": "cc.Vec2", "x": 132, "y": -21}, {"__type__": "cc.Vec2", "x": 132, "y": -17}, {"__type__": "cc.Vec2", "x": 130, "y": -14}, {"__type__": "cc.Vec2", "x": 130, "y": -5}, {"__type__": "cc.Vec2", "x": 134, "y": -2}, {"__type__": "cc.Vec2", "x": 134, "y": -12}, {"__type__": "cc.Vec2", "x": 137, "y": -16}, {"__type__": "cc.Vec2", "x": 138, "y": -10}, {"__type__": "cc.Vec2", "x": 145, "y": -7}, {"__type__": "cc.Vec2", "x": 149, "y": -1}, {"__type__": "cc.Vec2", "x": 150, "y": 8}, {"__type__": "cc.Vec2", "x": 149, "y": 13}, {"__type__": "cc.Vec2", "x": 146, "y": 16}, {"__type__": "cc.Vec2", "x": 146, "y": 20}, {"__type__": "cc.Vec2", "x": 144, "y": 21}, {"__type__": "cc.Vec2", "x": 144, "y": 24}, {"__type__": "cc.Vec2", "x": 147, "y": 26}, {"__type__": "cc.Vec2", "x": 147, "y": 31}, {"__type__": "cc.Vec2", "x": 153, "y": 33}, {"__type__": "cc.Vec2", "x": 153, "y": 39}, {"__type__": "cc.Vec2", "x": 156, "y": 42}, {"__type__": "cc.Vec2", "x": 156, "y": 56}, {"__type__": "cc.Vec2", "x": 154, "y": 60}, {"__type__": "cc.Vec2", "x": 150, "y": 63}, {"__type__": "cc.Vec2", "x": 140, "y": 63}, {"__type__": "cc.Vec2", "x": 137, "y": 61}, {"__type__": "cc.Vec2", "x": 135, "y": 57}, {"__type__": "cc.Vec2", "x": 134, "y": 50}, {"__type__": "cc.Vec2", "x": 128, "y": 48}, {"__type__": "cc.Vec2", "x": 121, "y": 48}, {"__type__": "cc.Vec2", "x": 114, "y": 53}, {"__type__": "cc.Vec2", "x": 109, "y": 53}, {"__type__": "cc.Vec2", "x": 108, "y": 55}, {"__type__": "cc.Vec2", "x": 110, "y": 77}, {"__type__": "cc.Vec2", "x": 107, "y": 81}, {"__type__": "cc.Vec2", "x": 76, "y": 83}, {"__type__": "cc.Vec2", "x": 72, "y": 87}, {"__type__": "cc.Vec2", "x": 71, "y": 91}, {"__type__": "cc.Vec2", "x": 65, "y": 91}, {"__type__": "cc.Vec2", "x": 65, "y": 83}, {"__type__": "cc.Vec2", "x": 62, "y": 83}, {"__type__": "cc.Vec2", "x": 60, "y": 84}, {"__type__": "cc.Vec2", "x": 59, "y": 93}, {"__type__": "cc.Vec2", "x": 55, "y": 94}, {"__type__": "cc.Vec2", "x": 49, "y": 92}, {"__type__": "cc.Vec2", "x": 47, "y": 89}, {"__type__": "cc.Vec2", "x": 49, "y": 81}, {"__type__": "cc.Vec2", "x": 55, "y": 75}, {"__type__": "cc.Vec2", "x": 53, "y": 74}, {"__type__": "cc.Vec2", "x": 44, "y": 81}, {"__type__": "cc.Vec2", "x": 42, "y": 94}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "307Lku2AtK+4g2ZGn0kX3Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]