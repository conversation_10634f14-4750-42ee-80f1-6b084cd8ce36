import {getInfo} from "../../api/user"
import {Toast} from "./Toast"

export default {
    gameLogId: null,
    token: null,
    availWidth: window.innerWidth,
    availHeight: window.innerHeight,
    canLottery: false,
    config: {
        customer_time: 2,
        limit_time: 120,
        open_ad: true,
        open_captcha: false,
    },
    userId: null,
    score: 30,
    directlyLottery: false,
    needGuide: false,

    setGameLogId(gameLogId: number) {
        this.gameLogId = gameLogId
    },
    setToken(token: string) {
        this.token = token
        localStorage.setItem("token", token)
    },
    setCanLottery(canLottery: boolean) {
        this.canLottery = canLottery
    },
    setConfig(config: any) {
        this.config = config
    },
    setUserId(userId: number) {
        this.userId = userId
    },
    setScore(score: number) {
        this.score = score
    },
    setDirectlyLottery(directlyLottery: boolean) {
        this.directlyLottery = directlyLottery
    },
    setNeedGuide(needGuide: boolean) {
        this.needGuide = needGuide
    },
    isLogin() {
        return this.token !== null
    },

    fetchInfo() {
        return new Promise((resolve, reject) => {
            getInfo({})
                .then((res) => {
                    if (res.code == 200) {
                        const {can_lottery, config, user_id} = res.result
                        this.setCanLottery(can_lottery)
                        this.setConfig(config)
                        this.setUserId(user_id)
                        resolve(res)
                    } else {
                        Toast.show(res.message, 3)
                        reject()
                    }
                })
                .catch((e) => {
                    Toast.show("网络繁忙，请稍后再试", 3)
                    console.error("getInfo fail !", e)
                    reject()
                })
        })
    },
}
