import { _decorator, Component, Node, Button, Event } from "cc"

const { ccclass, property } = _decorator
import { Dialog } from "./Dialog"
import EventManager from "./utils/EventManager"
import { EventEnum } from "./utils/Enum"

@ccclass("SuccessInGameAlert")
export class SuccessInGameAlert extends Dialog {
  @property(Button) goLotteryBtn: Button
  @property(Button) continueBtn: Button

  start() {
    super.start()

    this.goLotteryBtn.node.on(Button.EventType.CLICK, () => {
      this.hideDialog()
      EventManager.Instance.emit(EventEnum.GoLottery)
    })

    this.continueBtn.node.on(Button.EventType.CLICK, () => {
      this.hideDialog()
      EventManager.Instance.emit(EventEnum.Continue)
    })
  }

  update(deltaTime: number) {
    super.update(deltaTime)
  }
}
