import {
  _decorator,
  Component,
  Node,
  Label,
  Prefab,
  tween,
  Vec3,
  AudioClip,
  UITransform,
  instantiate,
  director,
  PhysicsSystem2D,
  Graphics,
  Vec2,
  CCFloat,
  Animation,
  RigidBody2D,
  BoxCollider2D,
  ERigidBody2DType,
  Size,
} from "cc"

const { ccclass, property } = _decorator

import { Toast } from "./utils/Toast"

import { AudioMgr } from "./AudioMgr"
import { startGame } from "../api/game"
import store from "../Script/utils/store"
import EventManager from "./utils/EventManager"
import { EventEnum } from "./utils/Enum"

import { SuccessAlert } from "./SuccessAlert"
import { SuccessWithNoPrizeAlert } from "./SuccessWithNoPrizeAlert"
import { FailAlert } from "./FailAlert"
import { LotteryAlert } from "./LotteryAlert"
import { AdPopup } from "./AdPopup"
import { WinningAlert } from "./WinningAlert"
import { DidntWinAlert } from "./DidntWinAlert"

@ccclass("Game")
export default class Game extends Component {
  @property(Label) labelCountDown: Label = null
  @property(Prefab) prefabSuccessAlert: Prefab = null
  @property(Prefab) prefabSuccessWithNoPrizeAlert: Prefab = null
  @property(Prefab) prefabFailAlert: Prefab = null
  @property(Prefab) prefabLotteryAlert: Prefab = null
  @property(Prefab) prefabAdPopup: Prefab = null
  @property(Prefab) prefabWinningAlert: Prefab = null
  @property(Prefab) prefabDidntWinAlert: Prefab = null
  @property(AudioClip) buttonAudioEffect: AudioClip = null
  @property(AudioClip) clearLineAudioEffect: AudioClip = null

  @property(AudioClip) gameSuccessSound: AudioClip
  @property(AudioClip) gameFailSound: AudioClip
  @property(AudioClip) lotterySuccessSound: AudioClip
  @property(AudioClip) lotteryFailSound: AudioClip
  @property(AudioClip) roundSuccessSound: AudioClip
  @property(AudioClip) roundFailSound: AudioClip
  // @property(Node) guideNode: Node

  @property(Node) containerNode: Node = null
  @property(Node) blocksNode: Node = null
  @property(Prefab) itemPrefab1: Prefab = null
  @property(Prefab) itemPrefab2: Prefab = null
  @property(Prefab) itemPrefab3: Prefab = null
  @property(Prefab) itemPrefab4: Prefab = null
  @property(Prefab) itemPrefab5: Prefab = null
  @property(Prefab) itemPrefab6: Prefab = null
  @property(Prefab) itemPrefab7: Prefab = null
  @property(Prefab) itemPrefab8: Prefab = null
  @property(Prefab) itemPrefab9: Prefab = null
  @property(Prefab) itemPrefab10: Prefab = null
  @property(Prefab) itemPrefab11: Prefab = null
  @property(Prefab) itemClearAnimation: Prefab = null


  @property({ type: CCFloat, tooltip: "Drop height above screen" })
  dropHeight: number = 800

  @property({ type: CCFloat, tooltip: "Drop interval between items (seconds)" })
  dropInterval: number = 0.1

  @property({ type: CCFloat, tooltip: "Ground Y position for physics collision" })
  groundY: number = -400

  private isRunning = false
  private countDown = 0
  private isRearranging = false // 新增：标记是否正在重新排列items
  private droppingItems: Node[] = [] // 正在掉落的物品
  private groundCollider: Node = null // 地面碰撞器

  private successAlertComponent: SuccessAlert = null
  private successWithNoPrizeAlertComponent: SuccessWithNoPrizeAlert = null
  private failAlertComponent: FailAlert = null
  private lotteryAlertComponent: LotteryAlert = null
  private adPopupComponent: AdPopup = null
  private winningAlertComponent: WinningAlert = null
  private didntWinAlertComponent: DidntWinAlert = null

  private countdownInterval: any = null

  private itemPrefabs: Prefab[] = []
  private blocks: Node[] = []
  private blockItems: any[] = []
  private reservedBlocks: boolean[] = [] // Track blocks that have items moving to them

  async start() {
    PhysicsSystem2D.instance.enable = true
    // 设置重力
    PhysicsSystem2D.instance.gravity = new Vec2(0, -980)

    // 创建地面碰撞器
    this.createGroundCollider()

    const successAlert = instantiate(this.prefabSuccessAlert)
    const successWithNoPrizeAlert = instantiate(
      this.prefabSuccessWithNoPrizeAlert
    )
    const failAlert = instantiate(this.prefabFailAlert)
    const lotteryAlert = instantiate(this.prefabLotteryAlert)
    const adPopup = instantiate(this.prefabAdPopup)
    const winningAlert = instantiate(this.prefabWinningAlert)
    const didntWinAlert = instantiate(this.prefabDidntWinAlert)

    const successAlertComponent = successAlert.getComponent(SuccessAlert)
    const successWithNoPrizeAlertComponent =
      successWithNoPrizeAlert.getComponent(SuccessWithNoPrizeAlert)
    const failAlertComponent = failAlert.getComponent(FailAlert)
    const lotteryAlertComponent = lotteryAlert.getComponent(LotteryAlert)
    const adPopupNodeComponent = adPopup.getComponent(AdPopup)
    const winningAlertNodeComponent = winningAlert.getComponent(WinningAlert)
    const didntWinAlertNodeComponent = didntWinAlert.getComponent(DidntWinAlert)

    this.itemPrefabs = [
      this.itemPrefab1,
      this.itemPrefab2,
      this.itemPrefab3,
      this.itemPrefab4,
      this.itemPrefab5,
      this.itemPrefab6,
      this.itemPrefab7,
      this.itemPrefab8,
      this.itemPrefab9,
      this.itemPrefab10,
      this.itemPrefab11,
    ]

    this.successAlertComponent = successAlertComponent
    this.successWithNoPrizeAlertComponent = successWithNoPrizeAlertComponent
    this.failAlertComponent = failAlertComponent
    this.lotteryAlertComponent = lotteryAlertComponent
    this.adPopupComponent = adPopupNodeComponent
    this.winningAlertComponent = winningAlertNodeComponent
    this.didntWinAlertComponent = didntWinAlertNodeComponent

    this.node.addChild(successAlert)
    this.node.addChild(successWithNoPrizeAlert)
    this.node.addChild(failAlert)
    this.node.addChild(lotteryAlert)
    this.node.addChild(winningAlert)
    this.node.addChild(didntWinAlert)
    this.node.addChild(adPopup)

    // Find the Container node if not set in the editor
    if (!this.containerNode) {
      this.containerNode = this.node.getChildByName("Container")
    }

    // Initialize blocks
    console.log("blocksNode是否存在:", !!this.blocksNode)
    if (this.blocksNode) {
      for (let i = 0; i < 7; i++) {
        const block = this.blocksNode.getChildByName(`block-${i}`)
        console.log(`查找block-${i}:`, !!block)
        if (block) {
          this.blocks.push(block)
          this.blockItems[i] = null // 初始化为null，表示没有item
          this.reservedBlocks[i] = false // 初始化为false，表示没有item正在移动到这个block
        }
      }
      console.log("blocks初始化完成，数量:", this.blocks.length)
    } else {
      // 尝试自动查找Blocks节点
      const blocksNode = this.node.getChildByName("Blocks")
      if (blocksNode) {
        console.log("自动查找到Blocks节点")
        this.blocksNode = blocksNode
        for (let i = 0; i < 7; i++) {
          const block = this.blocksNode.getChildByName(`block-${i}`)
          console.log(`查找block-${i}:`, !!block)
          if (block) {
            this.blocks.push(block)
            this.blockItems[i] = null // 初始化为null，表示没有item
            this.reservedBlocks[i] = false // 初始化为false，表示没有item正在移动到这个block
          }
        }
        console.log("blocks自动初始化完成，数量:", this.blocks.length)
      } else {
        console.error("未找到Blocks节点，请在编辑器中设置blocksNode属性")
      }
    }

    this.initEvent()

    if (store.directlyLottery) {
      setTimeout(() => {
        this.lotteryAlertComponent.showDialog()
      }, 500)
      store.setDirectlyLottery(false)
    } else {
      store.setDirectlyLottery(false)
      this.initGame()
    }

    this.adaptScreen()
  }

  onDestroy() {}

  /**
   * 创建地面碰撞器
   */
  createGroundCollider() {
    // 创建地面节点
    this.groundCollider = new Node("Ground")
    this.node.addChild(this.groundCollider)

    // 设置地面位置
    this.groundCollider.setPosition(0, this.groundY, 0)

    // 添加UITransform组件
    const uiTransform = this.groundCollider.addComponent(UITransform)
    uiTransform.width = 2000 // 足够宽的地面
    uiTransform.height = 50

    // 添加刚体组件
    const rigidBody = this.groundCollider.addComponent(RigidBody2D)
    rigidBody.type = ERigidBody2DType.Static // 静态刚体，不受重力影响

    // 添加盒子碰撞器组件
    const collider = this.groundCollider.addComponent(BoxCollider2D)
    collider.size = new Size(2000, 50) // 设置碰撞器大小
  }

  initEvent() {
    this.initOperate()
    EventManager.Instance.on(
      EventEnum.TryAgain,
      () => {
        this.initGame()
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.Continue,
      () => {
        this.continueGame()
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.GoLottery,
      () => {
        this.lotteryAlertComponent.showDialog()

        if (store.config.open_ad) {
          this.adPopupComponent.setFromLottery(true)
          this.adPopupComponent.showDialog()
        }
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.Win,
      (params) => {
        console.log("抽奖结果，中奖", params)
        this.winningAlertComponent.setPrize(params.win_amount)
        this.winningAlertComponent.showDialog()
        AudioMgr.inst.playOneShot(this.lotterySuccessSound, 1.5)
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.NoWin,
      () => {
        console.log("抽奖结果，未中奖")
        this.didntWinAlertComponent.showDialog()
        AudioMgr.inst.playOneShot(this.lotteryFailSound)
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.KnowMore,
      () => {
        this.adPopupComponent.setFromLottery(false)
        this.adPopupComponent.showDialog()
      },
      this
    )

    EventManager.Instance.on(
      EventEnum.GoAdLink,
      (params) => {
        const fromLottery = params.fromLottery
        if (store.canLottery && fromLottery) {
          localStorage.setItem("lotteryJumpAdAt", String(Date.now()))
          localStorage.setItem(
            "lotteryJumpAdGameLogId",
            String(store.gameLogId)
          )
          localStorage.setItem("token", store.token)
        } else {
          localStorage.removeItem("lotteryJumpAdAt")
          localStorage.removeItem("lotteryJumpAdGameLogId")
          localStorage.removeItem("token")
        }
        window.open("https://gfcrcbwx.csrcbank.com/new_mobile_banking_download")
      },
      this
    )
  }

  async initGame() {
    // 清空container
    this.containerNode?.removeAllChildren()

    // 清空掉落物品列表
    this.droppingItems = []

    // 清空所有blocks
    for (let i = 0; i < this.blocks.length; i++) {
      if (this.blocks[i]) {
        this.blocks[i].removeAllChildren()
        this.blockItems[i] = null
        this.reservedBlocks[i] = false // Reset reserved state
      }
    }

    // await store.fetchInfo()
    this.countDown = store.config.limit_time || 60
    this.renderCountDown()
    // Draw circle and initialize items
    this.initItemsInCircle()
    this.startGame()
  }

  /**
   * 初始化操作台
   */
  initOperate() {}

  async startGame() {
    let res: any
    // try {
    //   res = await startGame({})
    // } catch (error) {
    //   console.error("startGame error", error)
    //   Toast.show("游戏开始失败，请重试", 3)
    //   return
    // }
    // if (res?.code === 200) {
    //   store.setGameLogId(res.result.id)
    // } else {
    //   Toast.show(res.message, 3)
    //   setTimeout(() => {
    //     director.loadScene("welcome")
    //   }, 3000)
    //   return
    // }

    this.isRunning = true

    clearInterval(this.countdownInterval)
    this.countdownInterval = setInterval(() => {
      if (this.isRunning) {
        this.countDown--
        if (this.countDown <= 0) {
          clearInterval(this.countdownInterval)
          const isWin = false // todo
          this.endGame(isWin)
        }
        this.renderCountDown()
      }
    }, 1000)
  }

  renderCountDown() {
    // 60 -> 01:00
    let min = Math.floor(this.countDown / 60)
    let sec = this.countDown % 60
    let minStr = min < 10 ? "0" + min : min
    let secStr = sec < 10 ? "0" + sec : sec
    this.labelCountDown.string = `${minStr}:${secStr}`
  }

  // 继续游戏
  continueGame() {
    this.isRunning = true
  }

  endGame(success: boolean = false) {
    this.countDown = 0
    this.isRunning = false

    if (success) {
      AudioMgr.inst.playOneShot(this.gameSuccessSound)
      if (store.canLottery) {
        this.successAlertComponent.showDialog()
      } else {
        this.successWithNoPrizeAlertComponent.showDialog()
      }
    } else {
      AudioMgr.inst.playOneShot(this.gameFailSound)
      this.failAlertComponent.showDialog()
    }
  }

  update(deltaTime) {
    if (this.isRunning) {
    }
  }

  // 适配小屏幕
  adaptScreen() {
    const availHeight = document.getElementsByTagName("body")[0].offsetHeight
    console.log("height", availHeight)
    if (availHeight < 620) {
    } else if (availHeight < 720) {
      console.log("开始适配小屏幕")
    } else if (availHeight > 800) {
    } else {
    }
  }

  /**
   * 让物品从天而降，具有物理效果
   */
  initItemsInCircle() {
    // Clear existing items from container
    this.containerNode.removeAllChildren()
    this.droppingItems = []

    // Skip if no prefab is assigned
    if (!this.itemPrefabs.length) {
      console.warn("Item prefab not assigned!")
      return
    }

    console.log("初始化物品，数量:", this.itemPrefabs.length)

    // 计算总共要生成的item数量
    const totalItems = this.itemPrefabs.length * 6

    // 创建一个包含所有要生成物品类型的数组
    // 每个类型生成6个，所以每个类型的索引在数组中出现6次
    const itemTypesToGenerate: number[] = []
    for (let i = 0; i < this.itemPrefabs.length; i++) {
      for (let j = 0; j < 6; j++) {
        itemTypesToGenerate.push(i)
      }
    }

    // 随机打乱数组顺序
    this.shuffleArray(itemTypesToGenerate)

    console.log("打乱后的物品生成顺序:", itemTypesToGenerate)

    // 开始掉落物品
    this.startDroppingItems(itemTypesToGenerate)
  }

  /**
   * 开始掉落物品
   */
  private startDroppingItems(itemTypes: number[]) {
    let currentIndex = 0

    const dropNextItem = () => {
      if (currentIndex >= itemTypes.length) {
        console.log("所有物品掉落完成")
        return
      }

      const itemType = itemTypes[currentIndex]
      this.createDroppingItem(itemType)
      currentIndex++

      // 延迟掉落下一个物品
      this.scheduleOnce(dropNextItem, this.dropInterval)
    }

    // 开始掉落第一个物品
    dropNextItem()
  }

  /**
   * 创建一个掉落的物品
   */
  private createDroppingItem(itemType: number) {
    // Create item from prefab
    const item = instantiate(this.itemPrefabs[itemType])

    console.log("创建掉落物品 类型:", itemType)

    // 设置初始位置（屏幕上方随机位置）
    const randomX = (Math.random() - 0.5) * this.circleRadius * 1.5
    item.setPosition(randomX, this.dropHeight, 0)

    // 应用随机旋转
    const randomRotation = Math.random() * 360
    item.setRotationFromEuler(0, 0, randomRotation)

    // 确保物品有刚体组件并启用物理
    const rigidBody = item.getComponent(RigidBody2D)
    if (rigidBody) {
      rigidBody.type = ERigidBody2DType.Dynamic // 动态刚体，受重力影响
      rigidBody.gravityScale = 1 // 重力缩放
      rigidBody.linearDamping = 0.1 // 线性阻尼，让物品不会无限弹跳
      rigidBody.angularDamping = 0.5 // 角度阻尼，减少旋转
    }

    // 添加点击事件处理
    item.on("itemClick", () => {
      console.log("掉落物品被点击", itemType)
      if (this.isRunning && !this.isRearranging) {
        AudioMgr.inst.playOneShot(this.buttonAudioEffect)

        // Find the first empty block
        let targetBlockIndex = -1
        for (let k = 0; k < this.blocks.length; k++) {
          if (this.blockItems[k] === null && !this.reservedBlocks[k]) {
            targetBlockIndex = k
            break
          }
        }

        if (targetBlockIndex !== -1) {
          // 停止物理模拟，开始移动到目标位置
          this.moveItemToBlock(item, itemType, targetBlockIndex)
        }
      }
    })

    // 添加到容器
    this.containerNode.addChild(item)
    this.droppingItems.push(item)
  }

  /**
   * 将物品移动到目标block
   */
  private moveItemToBlock(item: Node, itemType: number, targetBlockIndex: number) {
    // Immediately mark this block as reserved
    this.reservedBlocks[targetBlockIndex] = true

    // 停止物理模拟
    const rigidBody = item.getComponent(RigidBody2D)
    if (rigidBody) {
      rigidBody.type = ERigidBody2DType.Static // 暂时设为静态，停止物理模拟
    }

    // Move item to the target block using world coordinates
    const targetBlock = this.blocks[targetBlockIndex]

    // 获取物品当前的世界坐标，以保持视觉连续性
    const itemWorldPos = new Vec3()
    item.getWorldPosition(itemWorldPos)

    // 获取目标Block的世界坐标
    const targetWorldPos = new Vec3()
    targetBlock.getWorldPosition(targetWorldPos)

    // 将物品从maskNode移到游戏根节点，以避免mask裁剪动画
    item.removeFromParent()
    this.node.addChild(item)

    // 设置在新父节点下的正确位置，保持视觉上的连续
    const newLocalPos = new Vec3()
    this.node.inverseTransformPoint(newLocalPos, itemWorldPos)
    item.setPosition(newLocalPos)

    // 从掉落物品列表中移除
    const index = this.droppingItems.indexOf(item)
    if (index > -1) {
      this.droppingItems.splice(index, 1)
    }

    // Animate item movement to target block
    tween(item)
      .parallel(
        // 位置动画 - 目标位置需要转换为新父节点(this.node)下的坐标
        tween().to(
          0.3,
          {
            position: this.node.inverseTransformPoint(
              new Vec3(),
              targetWorldPos
            ),
          },
          { easing: "cubicOut" }
        ),
        // 缩放动画，让物体越来越小
        tween().to(
          0.3,
          { scale: new Vec3(0.5, 0.5, 0.5) },
          { easing: "cubicIn" }
        ),
        // 旋转动画，回正角度
        tween().to(
          0.3,
          { eulerAngles: new Vec3(0, 0, 0) },
          { easing: "cubicOut" }
        )
      )
      .call(() => {
        console.log("动画完成，添加到block", targetBlockIndex)

        // 从当前父节点移除，添加到目标Block
        item.removeFromParent()
        targetBlock.addChild(item)

        // 重置item在新父节点下的位置和缩放
        item.setPosition(0, 0, 0)
        item.setScale(1, 1, 1)
        item.setRotationFromEuler(0, 0, 0)

        // 确保item是可见的
        item.active = true

        // item适应目标block的大小
        this.adjustItemSize(item, targetBlock)

        // Add item to block tracking
        this.blockItems[targetBlockIndex] = {
          node: item,
          type: itemType,
        }
        // Clear reserved status since the item is now in place
        this.reservedBlocks[targetBlockIndex] = false

        // 检查是否只剩一个空位
        let emptyCount = 0
        for (let k = 0; k < this.blockItems.length; k++) {
          if (this.blockItems[k] === null) {
            emptyCount++
          }
        }

        // 只有当只剩下一个空位且不会产生消除时才提示
        if (emptyCount === 1) {
          // 检查是否会产生消除
          const willMatch = this.checkWillMatch()

          // 只有当不会产生消除时才提示"仅剩一个空位"
          if (!willMatch) {
            Toast.show("仅剩一个空位！", 3)
          }
        }

        // Check for matches
        this.checkMatches()

        // 检查container中是否还有item，如果没有则游戏成功
        this.checkGameSuccess()

        // 如果没有找到匹配，额外检查一次是否所有格子已满
        const allFull = this.blockItems.every((item) => item !== null)
        if (allFull) {
          this.checkGameFailure()
        }
      })
      .start()
  }

  /**
   * Generate a random position inside the circle using rejection sampling
   * @param maxRadius - Maximum radius to ensure items don't exceed circle boundaries
   * @returns Vec2 position
   */
  getRandomPositionInCircle(maxRadius: number): Vec2 {
    // Choose a random angle and distance from center
    const angle = Math.random() * Math.PI * 2
    // Use square root to ensure uniform distribution
    const distance = Math.sqrt(Math.random()) * maxRadius

    // Convert polar to Cartesian coordinates
    const x = Math.cos(angle) * distance
    const y = Math.sin(angle) * distance

    return new Vec2(x, y)
  }

  private checkMatches() {
    // 按类型分组所有items
    const itemsByType = {}

    // 遍历所有block
    for (let i = 0; i < this.blockItems.length; i++) {
      const item = this.blockItems[i]
      if (item !== null) {
        const typeKey = String(item.type) // 确保类型键是字符串
        // 如果该类型不存在，初始化为空数组
        if (!itemsByType[typeKey]) {
          itemsByType[typeKey] = []
        }
        // 添加到对应类型的数组中
        itemsByType[typeKey].push({
          blockIndex: i,
          item: item,
        })
      }
    }

    console.log("按类型分组:", itemsByType)

    // 检查每种类型的item是否有三个或以上
    let hasMatch = false

    // 创建一个临时数组来存储所有要清除的items，防止在遍历时修改数组
    const itemsToRemove = []

    for (const typeKey in itemsByType) {
      if (itemsByType[typeKey].length >= 3) {
        hasMatch = true
        console.log(
          `发现匹配: 类型${typeKey}有${itemsByType[typeKey].length}个item`
        )

        // 标记要移除的items，但暂时不移除
        for (const itemInfo of itemsByType[typeKey]) {
          itemsToRemove.push(itemInfo)
        }

        // 播放消除音效
        AudioMgr.inst.playOneShot(this.clearLineAudioEffect)
      }
    }

    // 移除所有标记的items
    for (const itemInfo of itemsToRemove) {
      const blockIndex = itemInfo.blockIndex
      const item = itemInfo.item
      console.log(`正在移除: block索引${blockIndex}, 类型${item.type}`)

      // 先检查节点是否存在并有父节点
      if (item.node && item.node.isValid && item.node.parent) {
        item.node.removeFromParent()
      }

      // 播放消除动画
      if (this.itemClearAnimation) {
        const animation = instantiate(this.itemClearAnimation)
        console.log("播放消除动画", animation)
        const blockNode = this.blocks[blockIndex]

        blockNode.addChild(animation)
        animation.setPosition(0, 0, 0)

        // 确保动画大小合适
        const blockTransform = blockNode.getComponent(UITransform)
        const animTransform = animation.getComponent(UITransform)
        if (blockTransform && animTransform) {
          animTransform.width = blockTransform.width
          animTransform.height = blockTransform.height
        }

        // 确保动画可见并在最前方
        animation.active = true
        animation.setSiblingIndex(999)

        const animationComponent = animation.getComponent(Animation)
        if (animationComponent) {
          animationComponent.play()
          animationComponent.on(Animation.EventType.FINISHED, () => {
            console.log("动画结束")
            animation.destroy()
          })
        }
      }

      // 将该block标记为空
      this.blockItems[blockIndex] = null
    }

    // 如果有匹配，延迟一会儿再重新排列剩余的items，让动画有时间播放
    if (hasMatch) {
      // 设置正在重新排列的标志
      this.isRearranging = true

      this.scheduleOnce(() => {
        this.rearrangeItems()
      }, 0.3)
    } else {
      // 检查是否所有blocks都已填满，如果是且没有匹配，则游戏失败
      this.checkGameFailure()
    }
  }

  // 检查游戏是否失败（所有blocks都被占满且没有可消除的组合）
  private checkGameFailure() {
    // 检查是否所有blocks都已填满
    let allBlocksFilled = true
    for (let i = 0; i < this.blockItems.length; i++) {
      if (this.blockItems[i] === null) {
        allBlocksFilled = false
        break
      }
    }

    // 如果所有blocks都已填满，则检查是否有可能的匹配
    if (allBlocksFilled && this.isRunning) {
      // 检查是否有可能的匹配（3个或以上相同类型）
      const itemsByType = {}
      for (let i = 0; i < this.blockItems.length; i++) {
        const item = this.blockItems[i]
        const typeKey = String(item.type) // 确保类型键是字符串

        if (!itemsByType[typeKey]) {
          itemsByType[typeKey] = 0
        }
        itemsByType[typeKey]++
      }

      // 检查是否有任何类型出现了3次或以上
      let hasMatch = false
      for (const typeKey in itemsByType) {
        if (itemsByType[typeKey] >= 3) {
          hasMatch = true
          break
        }
      }

      // 如果所有格子都满了，且没有可消除的组合，则游戏失败
      if (!hasMatch) {
        console.log("所有格子都已填满，无法继续游戏，游戏失败！")
        this.isRunning = false
        clearInterval(this.countdownInterval)
        this.endGame(false)
      }
    }
  }

  // 重新排列剩余的items，让右边的物品向左移动填补空位
  private rearrangeItems() {
    // 首先，收集所有非空的items
    const nonEmptyItems = []
    for (let i = 0; i < this.blockItems.length; i++) {
      if (this.blockItems[i] !== null) {
        nonEmptyItems.push({
          blockIndex: i,
          item: this.blockItems[i],
        })
      }
    }

    console.log("重新排列前非空items数量:", nonEmptyItems.length)

    // 清空所有block
    for (let i = 0; i < this.blockItems.length; i++) {
      this.blockItems[i] = null
      // 清除block中的所有子节点
      this.blocks[i].removeAllChildren()
    }

    // 从左到右重新放置items，但现在使用物理掉落效果
    for (let i = 0; i < nonEmptyItems.length; i++) {
      const item = nonEmptyItems[i].item
      const node = item.node
      const type = item.type

      // 将item从block中移除，让它重新掉落到正确位置
      node.removeFromParent()
      this.containerNode.addChild(node)

      // 设置item到目标block上方的位置
      const targetBlock = this.blocks[i]
      const targetWorldPos = new Vec3()
      targetBlock.getWorldPosition(targetWorldPos)

      // 转换为containerNode的本地坐标
      const targetLocalPos = new Vec3()
      this.containerNode.inverseTransformPoint(targetLocalPos, targetWorldPos)

      // 设置到目标位置上方一定距离
      node.setPosition(targetLocalPos.x, targetLocalPos.y + 200, 0)

      // 重新启用物理效果
      const rigidBody = node.getComponent(RigidBody2D)
      if (rigidBody) {
        rigidBody.type = ERigidBody2DType.Dynamic
        rigidBody.linearVelocity = new Vec2(0, 0) // 重置速度
        rigidBody.angularVelocity = 0
      }

      // 添加到掉落物品列表
      this.droppingItems.push(node)

      // 延迟一点时间让物品自然掉落到block中
      this.scheduleOnce(() => {
        this.moveItemToBlock(node, type, i)
      }, 0.5 + i * 0.1) // 每个物品延迟不同时间，创造连续掉落效果
    }

    console.log("重新排列后items开始掉落")

    // 重新排列完成后，取消标记
    this.isRearranging = false
  }

  // 调整item大小以适应block
  private adjustItemSize(itemNode: Node, blockNode: Node) {
    const targetBlockTransform = blockNode.getComponent(UITransform)
    const itemTransform = itemNode.getComponent(UITransform)

    if (targetBlockTransform && itemTransform) {
      // 保存原始宽高比
      const originalWidth = itemTransform.width
      const originalHeight = itemTransform.height
      const aspectRatio = originalWidth / originalHeight

      // 计算适合block的尺寸，同时保持宽高比
      const blockWidth = targetBlockTransform.width * 0.8 // 留一些边距
      const blockHeight = targetBlockTransform.height * 0.8

      if (blockWidth / blockHeight > aspectRatio) {
        // 高度是限制因素
        itemTransform.height = blockHeight
        itemTransform.width = blockHeight * aspectRatio
      } else {
        // 宽度是限制因素
        itemTransform.width = blockWidth
        itemTransform.height = blockWidth / aspectRatio
      }
    }
  }

  // 新增方法：检查游戏是否成功（所有掉落的物品都被移走）
  private checkGameSuccess() {
    // 检查是否还有掉落的物品和containerNode中的物品
    const totalRemainingItems = this.containerNode.children.length + this.droppingItems.length
    if (totalRemainingItems === 0 && this.isRunning) {
      console.log("所有物品都已移走，游戏成功！")
      this.isRunning = false
      clearInterval(this.countdownInterval)
      this.endGame(true)
    }
  }

  /**
   * 随机打乱数组的方法
   */
  private shuffleArray(array: any[]) {
    for (let i = array.length - 1; i > 0; i--) {
      // 生成一个随机索引j，j在0到i之间（包括0和i）
      const j = Math.floor(Math.random() * (i + 1))
      // 交换array[i]和array[j]
      ;[array[i], array[j]] = [array[j], array[i]]
    }
    return array
  }

  /**
   * 检查是否会产生消除（3个或以上相同类型）
   * @returns 是否会产生消除
   */
  private checkWillMatch(): boolean {
    // 按类型分组所有items
    const itemsByType = {}

    // 遍历所有block
    for (let i = 0; i < this.blockItems.length; i++) {
      const item = this.blockItems[i]
      if (item !== null) {
        const typeKey = String(item.type) // 确保类型键是字符串
        // 如果该类型不存在，初始化为空数组
        if (!itemsByType[typeKey]) {
          itemsByType[typeKey] = 0
        }
        // 计数该类型的数量
        itemsByType[typeKey]++
      }
    }

    // 检查是否有任何类型的数量达到或超过3
    for (const typeKey in itemsByType) {
      if (itemsByType[typeKey] >= 3) {
        return true
      }
    }

    return false
  }
}
