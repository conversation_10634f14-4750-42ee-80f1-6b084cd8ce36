import {
  _decorator,
  Component,
  Node,
  Label,
  Prefab,
  tween,
  Vec3,
  AudioClip,
  UITransform,
  instantiate,
  director,
  PhysicsSystem2D,
  Graphics,
  Vec2,
  CCFloat,
  Animation,
} from "cc"

const { ccclass, property } = _decorator

import { Toast } from "./utils/Toast"

import { AudioMgr } from "./AudioMgr"
import { startGame } from "../api/game"
import store from "../Script/utils/store"
import EventManager from "./utils/EventManager"
import { EventEnum } from "./utils/Enum"

import { SuccessAlert } from "./SuccessAlert"
import { SuccessWithNoPrizeAlert } from "./SuccessWithNoPrizeAlert"
import { FailAlert } from "./FailAlert"
import { LotteryAlert } from "./LotteryAlert"
import { AdPopup } from "./AdPopup"
import { WinningAlert } from "./WinningAlert"
import { DidntWinAlert } from "./DidntWinAlert"

@ccclass("Game")
export default class Game extends Component {
  @property(Label) labelCountDown: Label = null
  @property(Prefab) prefabSuccessAlert: Prefab = null
  @property(Prefab) prefabSuccessWithNoPrizeAlert: Prefab = null
  @property(Prefab) prefabFailAlert: Prefab = null
  @property(Prefab) prefabLotteryAlert: Prefab = null
  @property(Prefab) prefabAdPopup: Prefab = null
  @property(Prefab) prefabWinningAlert: Prefab = null
  @property(Prefab) prefabDidntWinAlert: Prefab = null
  @property(AudioClip) buttonAudioEffect: AudioClip = null
  @property(AudioClip) clearLineAudioEffect: AudioClip = null

  @property(AudioClip) gameSuccessSound: AudioClip
  @property(AudioClip) gameFailSound: AudioClip
  @property(AudioClip) lotterySuccessSound: AudioClip
  @property(AudioClip) lotteryFailSound: AudioClip
  @property(AudioClip) roundSuccessSound: AudioClip
  @property(AudioClip) roundFailSound: AudioClip
  // @property(Node) guideNode: Node

  @property(Node) containerNode: Node = null
  @property(Node) blocksNode: Node = null
  @property(Prefab) itemPrefab1: Prefab = null
  @property(Prefab) itemPrefab2: Prefab = null
  @property(Prefab) itemPrefab3: Prefab = null
  @property(Prefab) itemPrefab4: Prefab = null
  @property(Prefab) itemPrefab5: Prefab = null
  @property(Prefab) itemPrefab6: Prefab = null
  @property(Prefab) itemPrefab7: Prefab = null
  @property(Prefab) itemPrefab8: Prefab = null
  @property(Prefab) itemPrefab9: Prefab = null
  @property(Prefab) itemPrefab10: Prefab = null
  @property(Prefab) itemPrefab11: Prefab = null
  @property(Prefab) itemClearAnimation: Prefab = null

  @property({ type: CCFloat, tooltip: "Circle radius in pixels" })
  circleRadius: number = 0

  private isRunning = false
  private countDown = 0
  private isRearranging = false // 新增：标记是否正在重新排列items

  private successAlertComponent: SuccessAlert = null
  private successWithNoPrizeAlertComponent: SuccessWithNoPrizeAlert = null
  private failAlertComponent: FailAlert = null
  private lotteryAlertComponent: LotteryAlert = null
  private adPopupComponent: AdPopup = null
  private winningAlertComponent: WinningAlert = null
  private didntWinAlertComponent: DidntWinAlert = null

  private countdownInterval: any = null

  private maskNode: Node = null

  private itemPrefabs: Prefab[] = []
  private blocks: Node[] = []
  private blockItems: any[] = []
  private reservedBlocks: boolean[] = [] // Track blocks that have items moving to them

  async start() {
    PhysicsSystem2D.instance.enable = true

    const successAlert = instantiate(this.prefabSuccessAlert)
    const successWithNoPrizeAlert = instantiate(
      this.prefabSuccessWithNoPrizeAlert
    )
    const failAlert = instantiate(this.prefabFailAlert)
    const lotteryAlert = instantiate(this.prefabLotteryAlert)
    const adPopup = instantiate(this.prefabAdPopup)
    const winningAlert = instantiate(this.prefabWinningAlert)
    const didntWinAlert = instantiate(this.prefabDidntWinAlert)

    const successAlertComponent = successAlert.getComponent(SuccessAlert)
    const successWithNoPrizeAlertComponent =
      successWithNoPrizeAlert.getComponent(SuccessWithNoPrizeAlert)
    const failAlertComponent = failAlert.getComponent(FailAlert)
    const lotteryAlertComponent = lotteryAlert.getComponent(LotteryAlert)
    const adPopupNodeComponent = adPopup.getComponent(AdPopup)
    const winningAlertNodeComponent = winningAlert.getComponent(WinningAlert)
    const didntWinAlertNodeComponent = didntWinAlert.getComponent(DidntWinAlert)

    this.itemPrefabs = [
      this.itemPrefab1,
      this.itemPrefab2,
      this.itemPrefab3,
      this.itemPrefab4,
      this.itemPrefab5,
      this.itemPrefab6,
      this.itemPrefab7,
      this.itemPrefab8,
      this.itemPrefab9,
      this.itemPrefab10,
      this.itemPrefab11,
    ]

    this.successAlertComponent = successAlertComponent
    this.successWithNoPrizeAlertComponent = successWithNoPrizeAlertComponent
    this.failAlertComponent = failAlertComponent
    this.lotteryAlertComponent = lotteryAlertComponent
    this.adPopupComponent = adPopupNodeComponent
    this.winningAlertComponent = winningAlertNodeComponent
    this.didntWinAlertComponent = didntWinAlertNodeComponent

    this.node.addChild(successAlert)
    this.node.addChild(successWithNoPrizeAlert)
    this.node.addChild(failAlert)
    this.node.addChild(lotteryAlert)
    this.node.addChild(winningAlert)
    this.node.addChild(didntWinAlert)
    this.node.addChild(adPopup)

    // Find the Container node if not set in the editor
    if (!this.containerNode) {
      this.containerNode = this.node.getChildByName("Container")
    }

    let maskNode = this.containerNode.getChildByName("MaskNode")
    if (maskNode) {
      this.maskNode = maskNode
      const uiTransform = maskNode.getComponent(UITransform)
      this.circleRadius = uiTransform.width / 2
      console.log("circleRadius", this.circleRadius)
    }

    // Initialize blocks
    console.log("blocksNode是否存在:", !!this.blocksNode)
    if (this.blocksNode) {
      for (let i = 0; i < 7; i++) {
        const block = this.blocksNode.getChildByName(`block-${i}`)
        console.log(`查找block-${i}:`, !!block)
        if (block) {
          this.blocks.push(block)
          this.blockItems[i] = null // 初始化为null，表示没有item
          this.reservedBlocks[i] = false // 初始化为false，表示没有item正在移动到这个block
        }
      }
      console.log("blocks初始化完成，数量:", this.blocks.length)
    } else {
      // 尝试自动查找Blocks节点
      const blocksNode = this.node.getChildByName("Blocks")
      if (blocksNode) {
        console.log("自动查找到Blocks节点")
        this.blocksNode = blocksNode
        for (let i = 0; i < 7; i++) {
          const block = this.blocksNode.getChildByName(`block-${i}`)
          console.log(`查找block-${i}:`, !!block)
          if (block) {
            this.blocks.push(block)
            this.blockItems[i] = null // 初始化为null，表示没有item
            this.reservedBlocks[i] = false // 初始化为false，表示没有item正在移动到这个block
          }
        }
        console.log("blocks自动初始化完成，数量:", this.blocks.length)
      } else {
        console.error("未找到Blocks节点，请在编辑器中设置blocksNode属性")
      }
    }

    this.initEvent()

    if (store.directlyLottery) {
      setTimeout(() => {
        this.lotteryAlertComponent.showDialog()
      }, 500)
      store.setDirectlyLottery(false)
    } else {
      store.setDirectlyLottery(false)
      this.initGame()
    }

    this.adaptScreen()
  }

  onDestroy() {}

  initEvent() {
    this.initOperate()
    EventManager.Instance.on(
      EventEnum.TryAgain,
      () => {
        this.initGame()
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.Continue,
      () => {
        this.continueGame()
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.GoLottery,
      () => {
        this.lotteryAlertComponent.showDialog()

        if (store.config.open_ad) {
          this.adPopupComponent.setFromLottery(true)
          this.adPopupComponent.showDialog()
        }
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.Win,
      (params) => {
        console.log("抽奖结果，中奖", params)
        this.winningAlertComponent.setPrize(params.win_amount)
        this.winningAlertComponent.showDialog()
        AudioMgr.inst.playOneShot(this.lotterySuccessSound, 1.5)
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.NoWin,
      () => {
        console.log("抽奖结果，未中奖")
        this.didntWinAlertComponent.showDialog()
        AudioMgr.inst.playOneShot(this.lotteryFailSound)
      },
      this
    )
    EventManager.Instance.on(
      EventEnum.KnowMore,
      () => {
        this.adPopupComponent.setFromLottery(false)
        this.adPopupComponent.showDialog()
      },
      this
    )

    EventManager.Instance.on(
      EventEnum.GoAdLink,
      (params) => {
        const fromLottery = params.fromLottery
        if (store.canLottery && fromLottery) {
          localStorage.setItem("lotteryJumpAdAt", String(Date.now()))
          localStorage.setItem(
            "lotteryJumpAdGameLogId",
            String(store.gameLogId)
          )
          localStorage.setItem("token", store.token)
        } else {
          localStorage.removeItem("lotteryJumpAdAt")
          localStorage.removeItem("lotteryJumpAdGameLogId")
          localStorage.removeItem("token")
        }
        window.open("https://gfcrcbwx.csrcbank.com/new_mobile_banking_download")
      },
      this
    )
  }

  async initGame() {
    // 清空container
    this.maskNode?.removeAllChildren()

    // 清空所有blocks
    for (let i = 0; i < this.blocks.length; i++) {
      if (this.blocks[i]) {
        this.blocks[i].removeAllChildren()
        this.blockItems[i] = null
        this.reservedBlocks[i] = false // Reset reserved state
      }
    }

    await store.fetchInfo()
    this.countDown = store.config.limit_time || 60
    this.renderCountDown()
    // Draw circle and initialize items
    if (this.maskNode) {
      this.initItemsInCircle()
      this.startGame()
    }
  }

  /**
   * 初始化操作台
   */
  initOperate() {}

  async startGame() {
    let res: any
    try {
      res = await startGame({})
    } catch (error) {
      console.error("startGame error", error)
      Toast.show("游戏开始失败，请重试", 3)
      return
    }
    if (res?.code === 200) {
      store.setGameLogId(res.result.id)
    } else {
      Toast.show(res.message, 3)
      setTimeout(() => {
        director.loadScene("welcome")
      }, 3000)
      return
    }

    this.isRunning = true

    clearInterval(this.countdownInterval)
    this.countdownInterval = setInterval(() => {
      if (this.isRunning) {
        this.countDown--
        if (this.countDown <= 0) {
          clearInterval(this.countdownInterval)
          const isWin = false // todo
          this.endGame(isWin)
        }
        this.renderCountDown()
      }
    }, 1000)
  }

  renderCountDown() {
    // 60 -> 01:00
    let min = Math.floor(this.countDown / 60)
    let sec = this.countDown % 60
    let minStr = min < 10 ? "0" + min : min
    let secStr = sec < 10 ? "0" + sec : sec
    this.labelCountDown.string = `${minStr}:${secStr}`
  }

  // 继续游戏
  continueGame() {
    this.isRunning = true
  }

  endGame(success: boolean = false) {
    this.countDown = 0
    this.isRunning = false

    if (success) {
      AudioMgr.inst.playOneShot(this.gameSuccessSound)
      if (store.canLottery) {
        this.successAlertComponent.showDialog()
      } else {
        this.successWithNoPrizeAlertComponent.showDialog()
      }
    } else {
      AudioMgr.inst.playOneShot(this.gameFailSound)
      this.failAlertComponent.showDialog()
    }
  }

  update(deltaTime) {
    if (this.isRunning) {
    }
  }

  // 适配小屏幕
  adaptScreen() {
    const availHeight = document.getElementsByTagName("body")[0].offsetHeight
    console.log("height", availHeight)
    if (availHeight < 620) {
    } else if (availHeight < 720) {
      console.log("开始适配小屏幕")
    } else if (availHeight > 800) {
    } else {
    }
  }

  /**
   * Load and place prefabs randomly inside the circle
   */
  initItemsInCircle() {
    // Clear existing items from container
    this.maskNode.removeAllChildren()

    // Skip if no prefab is assigned
    if (!this.itemPrefabs.length) {
      console.warn("Item prefab not assigned!")
      return
    }

    console.log("初始化物品，数量:", this.itemPrefabs.length)

    // 计算总共要生成的item数量
    const totalItems = this.itemPrefabs.length * 6

    // 创建一个包含所有要生成物品类型的数组
    // 每个类型生成6个，所以每个类型的索引在数组中出现6次
    const itemTypesToGenerate: number[] = []
    for (let i = 0; i < this.itemPrefabs.length; i++) {
      for (let j = 0; j < 6; j++) {
        itemTypesToGenerate.push(i)
      }
    }

    // 随机打乱数组顺序
    this.shuffleArray(itemTypesToGenerate)

    console.log("打乱后的物品生成顺序:", itemTypesToGenerate)

    // 预先生成所有位置，确保物品不会重叠
    const positions = this.generateNonOverlappingPositions(totalItems)

    // 按照打乱后的顺序生成物品
    for (let index = 0; index < itemTypesToGenerate.length; index++) {
      const itemType = itemTypesToGenerate[index]

      // Create item from prefab
      const item = instantiate(this.itemPrefabs[itemType])

      console.log("创建物品 类型:", itemType, "位置索引:", index)

      // 应用随机旋转
      const randomRotation = Math.random() * 360
      item.setRotationFromEuler(0, 0, randomRotation)

      item.on("itemClick", () => {
        console.log("物品被点击", itemType)
        if (this.isRunning && !this.isRearranging) {
          AudioMgr.inst.playOneShot(this.buttonAudioEffect)

          // Find the first empty block
          let targetBlockIndex = -1
          for (let k = 0; k < this.blocks.length; k++) {
            if (this.blockItems[k] === null && !this.reservedBlocks[k]) {
              // 检查block是否为空且没有被预定
              targetBlockIndex = k
              break
            }
          }

          console.log(
            "目标block索引:",
            targetBlockIndex,
            "blocks长度:",
            this.blocks.length
          )

          if (targetBlockIndex !== -1) {
            // Immediately mark this block as reserved
            this.reservedBlocks[targetBlockIndex] = true

            // Move item to the target block using world coordinates
            const targetBlock = this.blocks[targetBlockIndex]

            // 获取物品当前的世界坐标，以保持视觉连续性
            const itemWorldPos = new Vec3()
            item.getWorldPosition(itemWorldPos)

            // 获取目标Block的世界坐标
            const targetWorldPos = new Vec3()
            targetBlock.getWorldPosition(targetWorldPos)

            // 将物品从maskNode移到游戏根节点，以避免mask裁剪动画

            // 从原始父节点移除
            item.removeFromParent()

            // 添加到游戏根节点（或者其他不会裁剪的节点）
            this.node.addChild(item)

            // 设置在新父节点下的正确位置，保持视觉上的连续
            const newLocalPos = new Vec3()
            this.node.inverseTransformPoint(newLocalPos, itemWorldPos)
            item.setPosition(newLocalPos)

            // Animate item movement to target block
            tween(item)
              .parallel(
                // 位置动画 - 目标位置需要转换为新父节点(this.node)下的坐标
                tween().to(
                  0.3,
                  {
                    position: this.node.inverseTransformPoint(
                      new Vec3(),
                      targetWorldPos
                    ),
                  },
                  { easing: "cubicOut" }
                ),
                // 缩放动画，让物体越来越小
                tween().to(
                  0.3,
                  { scale: new Vec3(0.5, 0.5, 0.5) },
                  { easing: "cubicIn" }
                ),
                // 旋转动画，回正角度
                tween().to(
                  0.3,
                  { eulerAngles: new Vec3(0, 0, 0) },
                  { easing: "cubicOut" }
                )
              )
              .call(() => {
                console.log("动画完成，添加到block", targetBlockIndex)

                // 从当前父节点移除，添加到目标Block
                item.removeFromParent()
                targetBlock.addChild(item)

                // 重置item在新父节点下的位置和缩放
                item.setPosition(0, 0, 0)
                item.setScale(1, 1, 1)
                item.setRotationFromEuler(0, 0, 0)

                // 确保item是可见的
                item.active = true

                // item适应目标block的大小
                const targetBlockTransform =
                  targetBlock.getComponent(UITransform)
                const itemTransform = item.getComponent(UITransform)

                if (targetBlockTransform && itemTransform) {
                  // 不使用setContentSize，而是先保存原始宽高比
                  const originalWidth = itemTransform.width
                  const originalHeight = itemTransform.height
                  const aspectRatio = originalWidth / originalHeight

                  // 计算适合block的尺寸，同时保持宽高比
                  const blockWidth = targetBlockTransform.width * 0.8 // 留一些边距
                  const blockHeight = targetBlockTransform.height * 0.8

                  if (blockWidth / blockHeight > aspectRatio) {
                    // 高度是限制因素
                    itemTransform.height = blockHeight
                    itemTransform.width = blockHeight * aspectRatio
                  } else {
                    // 宽度是限制因素
                    itemTransform.width = blockWidth
                    itemTransform.height = blockWidth / aspectRatio
                  }

                  console.log(
                    "调整item尺寸:",
                    itemTransform.width,
                    itemTransform.height
                  )
                } else {
                  console.warn("找不到UITransform组件")
                }

                // Add item to block tracking
                this.blockItems[targetBlockIndex] = {
                  node: item,
                  type: itemType, // Store item type (0-9)
                }
                // Clear reserved status since the item is now in place
                this.reservedBlocks[targetBlockIndex] = false

                // 检查是否只剩一个空位
                let emptyCount = 0
                for (let k = 0; k < this.blockItems.length; k++) {
                  if (this.blockItems[k] === null) {
                    emptyCount++
                  }
                }

                // 只有当只剩下一个空位且不会产生消除时才提示
                if (emptyCount === 1) {
                  // 检查是否会产生消除
                  const willMatch = this.checkWillMatch()

                  // 只有当不会产生消除时才提示"仅剩一个空位"
                  if (!willMatch) {
                    Toast.show("仅剩一个空位！", 3)
                  }
                }

                // Check for matches
                this.checkMatches()

                // 检查container中是否还有item，如果没有则游戏成功
                this.checkGameSuccess()

                // 如果没有找到匹配，额外检查一次是否所有格子已满
                const allFull = this.blockItems.every((item) => item !== null)
                if (allFull) {
                  this.checkGameFailure()
                }
              })
              .start()
          }
        }
      })

      this.maskNode.addChild(item)

      // Get item size to ensure it doesn't go outside the circle
      const itemTransform = item.getComponent(UITransform)

      // 使用预先生成的位置
      if (index < positions.length) {
        item.setPosition(positions[index].x, positions[index].y, 0)
      } else {
        // 如果位置不够，使用原来的随机位置生成方法作为备选
        const maxRadius =
          this.circleRadius -
          Math.max(itemTransform.width, itemTransform.height) / 2
        let position = this.getRandomPositionInCircle(maxRadius)
        item.setPosition(position.x, position.y, 0)
      }
    }
  }

  /**
   * 生成不重叠的位置数组
   */
  private generateNonOverlappingPositions(count: number): Vec2[] {
    // 分层螺旋算法 + 完全重写
    const positions: Vec2[] = []

    // 将圆分成三个区域：外环、中环、内环
    const outerRingRadius = this.circleRadius * 0.7
    const middleRingRadius = this.circleRadius * 0.4
    const innerRingRadius = this.circleRadius * 0.1

    // 计算每个区域放多少物品
    const outerRingItems = Math.floor(count * 0.6) // 外环放x%的物品
    const middleRingItems = Math.floor(count * 0.3) // 中环放x%的物品
    const innerRingItems = count - outerRingItems - middleRingItems // 剩余的放内环

    // 生成外环物品位置
    this.generateItemsInRing(
      positions,
      outerRingItems,
      outerRingRadius,
      outerRingRadius * 0.15
    )

    // 生成中环物品位置
    this.generateItemsInRing(
      positions,
      middleRingItems,
      middleRingRadius,
      middleRingRadius * 0.15
    )

    // 生成内环物品位置
    this.generateItemsInRing(
      positions,
      innerRingItems,
      innerRingRadius,
      innerRingRadius * 0.15
    )

    // 随机打乱所有位置
    this.shuffleArray(positions)

    return positions
  }

  // 在指定环上生成物品位置
  private generateItemsInRing(
    positions: Vec2[],
    count: number,
    radius: number,
    variance: number
  ): void {
    // 平均分布在圆周上
    const angleStep = (2 * Math.PI) / count

    for (let i = 0; i < count; i++) {
      // 基础角度加上一点随机偏移
      const baseAngle = i * angleStep
      const randomAngleOffset = (Math.random() - 0.5) * angleStep * 0.7 // 角度偏移
      const finalAngle = baseAngle + randomAngleOffset

      // 半径也有随机偏移
      const randomRadiusOffset = (Math.random() - 0.5) * variance * 2
      const finalRadius = radius + randomRadiusOffset

      // 计算坐标
      const x = Math.cos(finalAngle) * finalRadius
      const y = Math.sin(finalAngle) * finalRadius

      positions.push(new Vec2(x, y))
    }
  }

  /**
   * Generate a random position inside the circle using rejection sampling
   * @param maxRadius - Maximum radius to ensure items don't exceed circle boundaries
   * @returns Vec2 position
   */
  getRandomPositionInCircle(maxRadius: number): Vec2 {
    // Choose a random angle and distance from center
    const angle = Math.random() * Math.PI * 2
    // Use square root to ensure uniform distribution
    const distance = Math.sqrt(Math.random()) * maxRadius

    // Convert polar to Cartesian coordinates
    const x = Math.cos(angle) * distance
    const y = Math.sin(angle) * distance

    return new Vec2(x, y)
  }

  private checkMatches() {
    // 按类型分组所有items
    const itemsByType = {}

    // 遍历所有block
    for (let i = 0; i < this.blockItems.length; i++) {
      const item = this.blockItems[i]
      if (item !== null) {
        const typeKey = String(item.type) // 确保类型键是字符串
        // 如果该类型不存在，初始化为空数组
        if (!itemsByType[typeKey]) {
          itemsByType[typeKey] = []
        }
        // 添加到对应类型的数组中
        itemsByType[typeKey].push({
          blockIndex: i,
          item: item,
        })
      }
    }

    console.log("按类型分组:", itemsByType)

    // 检查每种类型的item是否有三个或以上
    let hasMatch = false

    // 创建一个临时数组来存储所有要清除的items，防止在遍历时修改数组
    const itemsToRemove = []

    for (const typeKey in itemsByType) {
      if (itemsByType[typeKey].length >= 3) {
        hasMatch = true
        console.log(
          `发现匹配: 类型${typeKey}有${itemsByType[typeKey].length}个item`
        )

        // 标记要移除的items，但暂时不移除
        for (const itemInfo of itemsByType[typeKey]) {
          itemsToRemove.push(itemInfo)
        }

        // 播放消除音效
        AudioMgr.inst.playOneShot(this.clearLineAudioEffect)
      }
    }

    // 移除所有标记的items
    for (const itemInfo of itemsToRemove) {
      const blockIndex = itemInfo.blockIndex
      const item = itemInfo.item
      console.log(`正在移除: block索引${blockIndex}, 类型${item.type}`)

      // 先检查节点是否存在并有父节点
      if (item.node && item.node.isValid && item.node.parent) {
        item.node.removeFromParent()
      }

      // 播放消除动画
      if (this.itemClearAnimation) {
        const animation = instantiate(this.itemClearAnimation)
        console.log("播放消除动画", animation)
        const blockNode = this.blocks[blockIndex]

        blockNode.addChild(animation)
        animation.setPosition(0, 0, 0)

        // 确保动画大小合适
        const blockTransform = blockNode.getComponent(UITransform)
        const animTransform = animation.getComponent(UITransform)
        if (blockTransform && animTransform) {
          animTransform.width = blockTransform.width
          animTransform.height = blockTransform.height
        }

        // 确保动画可见并在最前方
        animation.active = true
        animation.setSiblingIndex(999)

        const animationComponent = animation.getComponent(Animation)
        if (animationComponent) {
          animationComponent.play()
          animationComponent.on(Animation.EventType.FINISHED, () => {
            console.log("动画结束")
            animation.destroy()
          })
        }
      }

      // 将该block标记为空
      this.blockItems[blockIndex] = null
    }

    // 如果有匹配，延迟一会儿再重新排列剩余的items，让动画有时间播放
    if (hasMatch) {
      // 设置正在重新排列的标志
      this.isRearranging = true

      this.scheduleOnce(() => {
        this.rearrangeItems()
      }, 0.3)
    } else {
      // 检查是否所有blocks都已填满，如果是且没有匹配，则游戏失败
      this.checkGameFailure()
    }
  }

  // 检查游戏是否失败（所有blocks都被占满且没有可消除的组合）
  private checkGameFailure() {
    // 检查是否所有blocks都已填满
    let allBlocksFilled = true
    for (let i = 0; i < this.blockItems.length; i++) {
      if (this.blockItems[i] === null) {
        allBlocksFilled = false
        break
      }
    }

    // 如果所有blocks都已填满，则检查是否有可能的匹配
    if (allBlocksFilled && this.isRunning) {
      // 检查是否有可能的匹配（3个或以上相同类型）
      const itemsByType = {}
      for (let i = 0; i < this.blockItems.length; i++) {
        const item = this.blockItems[i]
        const typeKey = String(item.type) // 确保类型键是字符串

        if (!itemsByType[typeKey]) {
          itemsByType[typeKey] = 0
        }
        itemsByType[typeKey]++
      }

      // 检查是否有任何类型出现了3次或以上
      let hasMatch = false
      for (const typeKey in itemsByType) {
        if (itemsByType[typeKey] >= 3) {
          hasMatch = true
          break
        }
      }

      // 如果所有格子都满了，且没有可消除的组合，则游戏失败
      if (!hasMatch) {
        console.log("所有格子都已填满，无法继续游戏，游戏失败！")
        this.isRunning = false
        clearInterval(this.countdownInterval)
        this.endGame(false)
      }
    }
  }

  // 重新排列剩余的items，让右边的物品向左移动填补空位
  private rearrangeItems() {
    // 首先，收集所有非空的items
    const nonEmptyItems = []
    for (let i = 0; i < this.blockItems.length; i++) {
      if (this.blockItems[i] !== null) {
        nonEmptyItems.push({
          blockIndex: i,
          item: this.blockItems[i],
        })
      }
    }

    console.log("重新排列前非空items数量:", nonEmptyItems.length)

    // 清空所有block
    for (let i = 0; i < this.blockItems.length; i++) {
      this.blockItems[i] = null
      // 清除block中的所有子节点
      this.blocks[i].removeAllChildren()
    }

    // 从左到右重新放置items
    for (let i = 0; i < nonEmptyItems.length; i++) {
      const item = nonEmptyItems[i].item
      const node = item.node
      const type = item.type

      // 将item放入新位置
      this.blocks[i].addChild(node)

      // 重置item在新父节点下的位置和缩放
      node.setPosition(0, 0, 0)
      node.setScale(1, 1, 1)

      // 确保item是可见的
      node.active = true

      // 调整item大小适应block
      this.adjustItemSize(node, this.blocks[i])

      // 更新blockItems数组
      this.blockItems[i] = {
        node: node,
        type: type,
      }

      // 添加动画效果，加快动画速度
      tween(node)
        .to(0.15, { scale: new Vec3(1.2, 1.2, 1.2) })
        .to(0.15, { scale: new Vec3(1, 1, 1) })
        .start()
    }

    console.log("重新排列后items状态:", this.blockItems)

    // 重新排列完成后，取消标记
    this.isRearranging = false
  }

  // 调整item大小以适应block
  private adjustItemSize(itemNode: Node, blockNode: Node) {
    const targetBlockTransform = blockNode.getComponent(UITransform)
    const itemTransform = itemNode.getComponent(UITransform)

    if (targetBlockTransform && itemTransform) {
      // 保存原始宽高比
      const originalWidth = itemTransform.width
      const originalHeight = itemTransform.height
      const aspectRatio = originalWidth / originalHeight

      // 计算适合block的尺寸，同时保持宽高比
      const blockWidth = targetBlockTransform.width * 0.8 // 留一些边距
      const blockHeight = targetBlockTransform.height * 0.8

      if (blockWidth / blockHeight > aspectRatio) {
        // 高度是限制因素
        itemTransform.height = blockHeight
        itemTransform.width = blockHeight * aspectRatio
      } else {
        // 宽度是限制因素
        itemTransform.width = blockWidth
        itemTransform.height = blockWidth / aspectRatio
      }
    }
  }

  // 新增方法：检查游戏是否成功（所有container中的item都被移走）
  private checkGameSuccess() {
    if (this.maskNode.children.length === 0 && this.isRunning) {
      console.log("所有物品都已移走，游戏成功！")
      this.isRunning = false
      clearInterval(this.countdownInterval)
      this.endGame(true)
    }
  }

  /**
   * 随机打乱数组的方法
   */
  private shuffleArray(array: any[]) {
    for (let i = array.length - 1; i > 0; i--) {
      // 生成一个随机索引j，j在0到i之间（包括0和i）
      const j = Math.floor(Math.random() * (i + 1))
      // 交换array[i]和array[j]
      ;[array[i], array[j]] = [array[j], array[i]]
    }
    return array
  }

  /**
   * 检查是否会产生消除（3个或以上相同类型）
   * @returns 是否会产生消除
   */
  private checkWillMatch(): boolean {
    // 按类型分组所有items
    const itemsByType = {}

    // 遍历所有block
    for (let i = 0; i < this.blockItems.length; i++) {
      const item = this.blockItems[i]
      if (item !== null) {
        const typeKey = String(item.type) // 确保类型键是字符串
        // 如果该类型不存在，初始化为空数组
        if (!itemsByType[typeKey]) {
          itemsByType[typeKey] = 0
        }
        // 计数该类型的数量
        itemsByType[typeKey]++
      }
    }

    // 检查是否有任何类型的数量达到或超过3
    for (const typeKey in itemsByType) {
      if (itemsByType[typeKey] >= 3) {
        return true
      }
    }

    return false
  }
}
