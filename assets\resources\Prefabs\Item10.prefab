[{"__type__": "cc.Prefab", "_name": "Item10", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item10", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f6c5f5ee-c928-490d-9581-3951d4c08b4c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -17, "y": 55}, {"__type__": "cc.Vec2", "x": -29, "y": 52}, {"__type__": "cc.Vec2", "x": -32, "y": 49}, {"__type__": "cc.Vec2", "x": -34, "y": 49}, {"__type__": "cc.Vec2", "x": -42, "y": 41}, {"__type__": "cc.Vec2", "x": -47, "y": 31}, {"__type__": "cc.Vec2", "x": -51, "y": 11}, {"__type__": "cc.Vec2", "x": -50, "y": -8}, {"__type__": "cc.Vec2", "x": -54, "y": -11}, {"__type__": "cc.Vec2", "x": -54, "y": -17}, {"__type__": "cc.Vec2", "x": -56, "y": -18}, {"__type__": "cc.Vec2", "x": -58, "y": -23}, {"__type__": "cc.Vec2", "x": -58, "y": -30}, {"__type__": "cc.Vec2", "x": -56, "y": -32}, {"__type__": "cc.Vec2", "x": -52, "y": -31}, {"__type__": "cc.Vec2", "x": -52, "y": -29}, {"__type__": "cc.Vec2", "x": -49, "y": -26}, {"__type__": "cc.Vec2", "x": -47, "y": -17}, {"__type__": "cc.Vec2", "x": -44, "y": -16}, {"__type__": "cc.Vec2", "x": -44, "y": -28}, {"__type__": "cc.Vec2", "x": -38, "y": -40}, {"__type__": "cc.Vec2", "x": -29, "y": -48}, {"__type__": "cc.Vec2", "x": -11, "y": -55}, {"__type__": "cc.Vec2", "x": 23, "y": -55}, {"__type__": "cc.Vec2", "x": 35, "y": -52}, {"__type__": "cc.Vec2", "x": 47, "y": -46}, {"__type__": "cc.Vec2", "x": 54, "y": -39}, {"__type__": "cc.Vec2", "x": 58, "y": -30}, {"__type__": "cc.Vec2", "x": 58, "y": -13}, {"__type__": "cc.Vec2", "x": 52, "y": 2}, {"__type__": "cc.Vec2", "x": 46, "y": 8}, {"__type__": "cc.Vec2", "x": 38, "y": 10}, {"__type__": "cc.Vec2", "x": 31, "y": 15}, {"__type__": "cc.Vec2", "x": 31, "y": 17}, {"__type__": "cc.Vec2", "x": 36, "y": 22}, {"__type__": "cc.Vec2", "x": 36, "y": 28}, {"__type__": "cc.Vec2", "x": 30, "y": 39}, {"__type__": "cc.Vec2", "x": 26, "y": 41}, {"__type__": "cc.Vec2", "x": 23, "y": 45}, {"__type__": "cc.Vec2", "x": 12, "y": 51}, {"__type__": "cc.Vec2", "x": -4, "y": 55}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85fwHvMFhBH6HJPHoSFoEj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]