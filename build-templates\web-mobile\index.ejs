﻿<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />

    <title>抓金粽-粽享端午</title>

    <!--http://www.html5rocks.com/en/mobile/mobifying/-->
    <meta
      name="viewport"
      content="width=device-width,user-scalable=no,initial-scale=1,minimum-scale=1,maximum-scale=1,minimal-ui=true,viewport-fit=cover"
    />

    <!--https://developer.apple.com/library/safari/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html-->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="format-detection" content="telephone=no" />

    <!-- force webkit on 360 -->
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <!-- force edge on IE -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="msapplication-tap-highlight" content="no" />

    <!-- force full screen on some browser -->
    <meta name="full-screen" content="yes" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="360-fullscreen" content="true" />

    <!--fix fireball/issues/3568 -->
    <!--<meta name="browsermode" content="application">-->
    <meta name="x5-page-mode" content="app" />

    <!--<link rel="apple-touch-icon" href=".png" />-->
    <!--<link rel="apple-touch-icon-precomposed" href=".png" />-->

    <link rel="stylesheet" type="text/css" href="<%= cssUrl %>" />
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script src="https://turing.captcha.qcloud.com/TJCaptcha.js"></script>

    <style>
      #splash {
        position: absolute;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background-image: url("./splash-bg-1.png");
        background-size: cover;
        background-position: bottom center;
      }

      #splash::after {
        content: "";
        position: absolute;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.85);
      }

      .logo {
        width: 300px;
        height: 19.35px;
        position: relative;
        top: -80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 1;
      }

      .logo .loading-icons {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .logo .loading-icons img.icon {
        width: 38px;
        height: 38px;
        margin: 0 10px;
        animation: float 0.8s ease-in-out infinite;
      }

      .logo .loading-icons img.icon:nth-child(1) {
        animation-delay: 0s;
      }

      .logo .loading-icons img.icon:nth-child(2) {
        animation-delay: 0.15s;
      }

      .logo .loading-icons img.icon:nth-child(3) {
        animation-delay: 0.3s;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-15px);
        }
      }

      .logo .loading-text {
        /* width: 58px; */
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 17px;
        margin-top: 20px;
      }

      .logo .loading-text img {
        width: auto;
        height: 100%;
        vertical-align: middle;
      }

      .progress {
        font-size: 18px;
        color: #fcd25b;
        margin-top: 1px;
        margin-left: 8px;
        line-height: 1;
      }

      #launch-btn {
        position: absolute;
        background-image: url("./know-more-btn2.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    </style>
  </head>
  <body>
    <div id="GameDiv" cc_exact_fit_screen="true">
      <div id="Cocos3dGameContainer">
        <canvas
          id="GameCanvas"
          oncontextmenu="event.preventDefault()"
          tabindex="99"
        ></canvas>
      </div>
    </div>
    <div id="splash">
      <div class="logo">
        <div class="loading-icons">
          <img class="icon" src="./loading01.png" alt="" />
          <img class="icon" src="./loading02.png" alt="" />
          <img class="icon" src="./loading03.png" alt="" />
        </div>
        <div class="loading-text">
          <img src="./loading-text.png" alt="" />
          <span class="progress">0%</span>
        </div>
      </div>
    </div>

    <wx-open-launch-weapp
      id="launch-btn"
      appid="wx5d196b363f4b3cb7"
      path="modules-bus/pages/index.html"
      style="display: none"
    >
      <script type="text/wxtag-template">
        <style>
            #launch-ad-btn {
                width: 88px;
                height: 29px;
            }
        </style>
        <div id="launch-ad-btn"></div>
      </script>
    </wx-open-launch-weapp>

    <%- include(cocosTemplate, {}) %>

    <script>
      let progress = 0
      let progressBar = document.querySelector(".progress")
      let loadCcSuccess = false

      setTimeout(() => {
        if (!loadCcSuccess) {
          progressUpdate(100, 5)
          document.getElementById("splash").style.display = "none"
        }
      }, 10000)

      function onProgress() {
        progressUpdate(50, 120)
        cc.loader.onProgress = function (completedCount, totalCount, item) {
          let reallyProgress = ((completedCount / totalCount) * 100) | 0
          progressUpdate(reallyProgress, 5)
        }
        cc.director.once(cc.Director.EVENT_AFTER_SCENE_LAUNCH, () => {
          progressUpdate(100, 5)
          setTimeout(() => {
            progress = 100
            progressBar.innerHTML = "100%"
            document.getElementById("splash").style.display = "none"
          }, 1000)
        })
      }

      let interval = setInterval(() => {
        console.info(progress)
        progress += 1
        progressBar.innerHTML = progress + "%"

        console.log(progress)
        if (typeof cc !== "undefined") {
          console.log("cc", cc)
          loadCcSuccess = true
          clearInterval(interval)
          onProgress()
        }
        if (progress >= 100) {
          clearInterval(interval)
        }
      }, 20)

      var baseUrl = "https://zhuajinzongapi.ziboxiaochengxu.com"
      // var baseUrl = "http://10.0.0.7:20003"
      localStorage.setItem("baseUrl", baseUrl)
      fetch(`${baseUrl}/api/user/js_sdk_config`, {
        method: "POST",
        body: JSON.stringify({ url: location.href.split("#")[0] }),
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then((res) => res.json())
        .then((res) => {
          console.log("res", res)
          wx.config({
            ...res.result,
            debug: false,
          })
          wx.ready(() => {
            wx.hideMenuItems({
              menuList: [
                "menuItem:favorite",
                "menuItem:share:appMessage",
                "menuItem:share:timeline",
                "menuItem:share:qq",
                "menuItem:share:weiboApp",
                "menuItem:share:facebook",
                "menuItem:share:QZone",
                "menuItem:copyUrl",
                "menuItem:openWithQQBrowser",
                "menuItem:openWithSafari",
                "menuItem:share:email",
                "menuItem:share:brand",
              ],
            })
          })
        })

      let progressInterval = null

      function progressUpdate(nowProcess, timeInterval = 20) {
        if (nowProcess > progress) {
          if (progressInterval) {
            clearInterval(progressInterval)
            progressInterval = null
          }
          progressInterval = setInterval(() => {
            if (progress < nowProcess) {
              progress += 1
              progressBar.innerHTML = progress + "%"
            } else {
              clearInterval(progressInterval)
              progressInterval = null
            }
            console.log(nowProcess, progress, timeInterval)
          }, timeInterval)
        }
      }
    </script>
  </body>
</html>
