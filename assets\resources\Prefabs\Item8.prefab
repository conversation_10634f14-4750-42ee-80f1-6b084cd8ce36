[{"__type__": "cc.Prefab", "_name": "Item8", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item8", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 92, "height": 255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "31508529-02d2-46ca-a678-7e5e6707d9b2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -22, "y": 127.5}, {"__type__": "cc.Vec2", "x": -26, "y": 125.5}, {"__type__": "cc.Vec2", "x": -43, "y": 122.5}, {"__type__": "cc.Vec2", "x": -46, "y": 119.5}, {"__type__": "cc.Vec2", "x": -46, "y": 114.5}, {"__type__": "cc.Vec2", "x": -44, "y": 112.5}, {"__type__": "cc.Vec2", "x": -33, "y": 112.5}, {"__type__": "cc.Vec2", "x": 3, "y": -31.5}, {"__type__": "cc.Vec2", "x": 3, "y": -34.5}, {"__type__": "cc.Vec2", "x": -4, "y": -46.5}, {"__type__": "cc.Vec2", "x": -4, "y": -53.5}, {"__type__": "cc.Vec2", "x": 3, "y": -79.5}, {"__type__": "cc.Vec2", "x": 3, "y": -84.5}, {"__type__": "cc.Vec2", "x": 5, "y": -88.5}, {"__type__": "cc.Vec2", "x": 5, "y": -93.5}, {"__type__": "cc.Vec2", "x": 8, "y": -101.5}, {"__type__": "cc.Vec2", "x": 13, "y": -127.5}, {"__type__": "cc.Vec2", "x": 46, "y": -118.5}, {"__type__": "cc.Vec2", "x": 25, "y": -44.5}, {"__type__": "cc.Vec2", "x": 14, "y": -34.5}, {"__type__": "cc.Vec2", "x": 12, "y": -34.5}, {"__type__": "cc.Vec2", "x": 9, "y": -29.5}, {"__type__": "cc.Vec2", "x": 9, "y": -24.5}, {"__type__": "cc.Vec2", "x": 5, "y": -12.5}, {"__type__": "cc.Vec2", "x": -1, "y": 18.5}, {"__type__": "cc.Vec2", "x": -5, "y": 30.5}, {"__type__": "cc.Vec2", "x": -8, "y": 48.5}, {"__type__": "cc.Vec2", "x": -24, "y": 112.5}, {"__type__": "cc.Vec2", "x": -24, "y": 115.5}, {"__type__": "cc.Vec2", "x": -16, "y": 117.5}, {"__type__": "cc.Vec2", "x": -14, "y": 122.5}, {"__type__": "cc.Vec2", "x": -16, "y": 126.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19i/h0VaRPBb1pSNCC50Js"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]