[{"__type__": "cc.Prefab", "_name": "Item2", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item2", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f6dbca3d-22ae-41c1-b70e-1d06fffd4cd4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -5.5, "y": 62}, {"__type__": "cc.Vec2", "x": -17.5, "y": 56}, {"__type__": "cc.Vec2", "x": -27.5, "y": 46}, {"__type__": "cc.Vec2", "x": -27.5, "y": 44}, {"__type__": "cc.Vec2", "x": -30.5, "y": 42}, {"__type__": "cc.Vec2", "x": -30.5, "y": 40}, {"__type__": "cc.Vec2", "x": -37.5, "y": 32}, {"__type__": "cc.Vec2", "x": -42.5, "y": 21}, {"__type__": "cc.Vec2", "x": -44.5, "y": 20}, {"__type__": "cc.Vec2", "x": -65.5, "y": -27}, {"__type__": "cc.Vec2", "x": -66.5, "y": -40}, {"__type__": "cc.Vec2", "x": -64.5, "y": -46}, {"__type__": "cc.Vec2", "x": -57.5, "y": -53}, {"__type__": "cc.Vec2", "x": -49.5, "y": -57}, {"__type__": "cc.Vec2", "x": -27.5, "y": -62}, {"__type__": "cc.Vec2", "x": 40.5, "y": -62}, {"__type__": "cc.Vec2", "x": 58.5, "y": -56}, {"__type__": "cc.Vec2", "x": 63.5, "y": -52}, {"__type__": "cc.Vec2", "x": 63.5, "y": -50}, {"__type__": "cc.Vec2", "x": 66.5, "y": -47}, {"__type__": "cc.Vec2", "x": 66.5, "y": -29}, {"__type__": "cc.Vec2", "x": 63.5, "y": -20}, {"__type__": "cc.Vec2", "x": 45.5, "y": 14}, {"__type__": "cc.Vec2", "x": 43.5, "y": 15}, {"__type__": "cc.Vec2", "x": 39.5, "y": 23}, {"__type__": "cc.Vec2", "x": 36.5, "y": 25}, {"__type__": "cc.Vec2", "x": 34.5, "y": 30}, {"__type__": "cc.Vec2", "x": 31.5, "y": 32}, {"__type__": "cc.Vec2", "x": 31.5, "y": 34}, {"__type__": "cc.Vec2", "x": 14.5, "y": 53}, {"__type__": "cc.Vec2", "x": 12.5, "y": 53}, {"__type__": "cc.Vec2", "x": 9.5, "y": 57}, {"__type__": "cc.Vec2", "x": 2.5, "y": 61}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ab31Ed/3VCBqFOnrmmXuj7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]