import {_decorator, Component, Node, Label, UITransform, Color, Graphics, Size, view, find} from 'cc';

const {ccclass, property} = _decorator;

@ccclass('Toast')
export class Toast extends Component {
    static show(message: string, duration: number = 2, type = "center") {
        let maxWidth = view.getVisibleSize().width * 0.8;
        const padding = 25;

        const canvas = find('Canvas');
        if (!canvas) return;

        // 创建 Toast 节点
        const toastNode = new Node('Toast');
        // 创建背景节点
        const bgNode = new Node('Background');
        canvas.addChild(bgNode);
        canvas.addChild(toastNode);

        // 添加 Graphics 组件用于绘制背景
        const graphics = bgNode.addComponent(Graphics);

        // 添加 Label 组件，并确保它位于背景之上
        const label = toastNode.addComponent(Label);
        label.string = message;
        label.fontSize = 30;
        label.color = new Color(255, 255, 255, 0); // 完全不透明的白色
        // label.overflow = Label.Overflow.NONE; // 设置为无溢出
        const labelUITransform = label.node.getComponent(UITransform);
        let visibleSize = view.getVisibleSize()
        if (message.length * label.fontSize > visibleSize.width * 0.8) {
            labelUITransform.width = visibleSize.width * 0.8 - padding * 2;
            label.overflow = Label.Overflow.RESIZE_HEIGHT;
        }

        // 等待下一帧以确保 Label 尺寸已更新
        setTimeout(() => {
            label.color = new Color(255, 255, 255, 255); // 完全不透明的白色

            // 调整 Toast 和背景尺寸
            const bgWidth = Math.min(labelUITransform.width + padding * 2, maxWidth);
            const bgHeight = labelUITransform.height + padding * 2;

            // 绘制圆角矩形背景
            graphics.roundRect(-bgWidth / 2, -bgHeight / 2, bgWidth, bgHeight, 20);
            graphics.fillColor = new Color(0, 0, 0, 200);
            graphics.fill();

            // 设置背景节点位置和大小
            const bgUITransform = bgNode.addComponent(UITransform);
            bgUITransform.setContentSize(bgWidth, bgHeight);
            bgNode.setPosition(0, 0, 0);

            // 设置 Label 位置，确保在背景之上
            label.node.setPosition(0, 0, 1);

            // 计算并设置 Toast 节点靠屏幕上方的位置
            const screenHeight = view.getVisibleSize().height;

            if (type === "top") {
                toastNode.setPosition(0, screenHeight / 2 - bgHeight / 2 - 50, 0);
                bgNode.setPosition(0, screenHeight / 2 - bgHeight / 2 - 50, 0);
            } else if (type === "center") {
                toastNode.setPosition(0, 0, 0);
                bgNode.setPosition(0, 0, 0);
            }
            // 设置自动隐藏和销毁
            setTimeout(() => {
                toastNode.removeFromParent();
                toastNode.destroy();
                bgNode.removeFromParent()
                bgNode.destroy()
            }, duration * 1000);
        }, 100);
    }
}
