import { _decorator, Node, Button, Sprite, Sprite<PERSON><PERSON><PERSON>, AudioClip } from "cc"

const { ccclass, property } = _decorator
import { Dialog } from "./Dialog"
import { EventEnum } from "./utils/Enum"
import EventManager from "./utils/EventManager"
import { lottery, transfer } from "../api/game"
import store from "./utils/store"
import { Toast } from "./utils/Toast"
import { AudioMgr } from "./AudioMgr"

@ccclass("LotteryAlert")
export class LotteryAlert extends Dialog {
  @property(Node) lotteryBg: Node

  @property(SpriteFrame) lotteryBtnSpriteFrame: SpriteFrame
  @property(SpriteFrame) inLotteryBtnSpriteFrame: SpriteFrame

  @property(Button) lotteryBtn: Button
  @property(AudioClip) lotterySound: AudioClip
  @property(AudioClip) lotteryResultSound: AudioClip

  @property(SpriteFrame) redPocketFrame: SpriteFrame
  @property(SpriteFrame) activeRedPocketFrame: SpriteFrame

  private inLottery: boolean = false

  private ticket: string = ""
  private randstr: string = ""

  timerInterval = null

  start() {
    super.start()
    const { open_captcha } = store.config
    this.lotteryBtn.node.on(Button.EventType.CLICK, async () => {
      if (this.inLottery) return
      this.lotteryBtn.getComponent(Sprite).spriteFrame =
        this.inLotteryBtnSpriteFrame
      if (open_captcha) {
        let res: any
        try {
          res = await this.captcha()
        } catch (e) {
          console.error(e)
        }

        if (res?.ret === 0) {
          this.ticket = res.ticket
          this.randstr = res.randstr
          this.lotteryAnimation()
        } else {
          Toast.show("请完成验证", 3)
          this.inLottery = false
          this.lotteryBtn.getComponent(Sprite).spriteFrame =
            this.lotteryBtnSpriteFrame
        }
      } else {
        this.lotteryAnimation()
      }
    })
  }

  update(deltaTime: number) {
    super.update(deltaTime)
  }

  showDialog(x: number = 0, y: number = 0) {
    console.log("showLotteryDialog")
    super.showDialog(x, y)
  }

  async lotteryAnimation() {
    console.log("lotteryAnimation")
    if (this.inLottery) return
    this.lotteryBtn.getComponent(Sprite).spriteFrame =
      this.inLotteryBtnSpriteFrame
    this.inLottery = true
    AudioMgr.inst.playOneShot(this.lotterySound)

    // 闪烁红包
    let activeIndex = 0
    this.timerInterval = setInterval(() => {
      // 重置所有红包为非激活状态
      for (let i = 0; i <= 7; i++) {
        const node = this.lotteryBg.getChildByName(`Node${i}`)
        if (node) {
          const nodeSprite = node.getComponent(Sprite)
          if (nodeSprite) {
            nodeSprite.spriteFrame = this.redPocketFrame
          }
        }
      }
      // 设置当前索引的红包为激活状态
      const nodeToActivate = this.lotteryBg.getChildByName(`Node${activeIndex}`)
      if (nodeToActivate) {
        const sprite = nodeToActivate.getComponent(Sprite)
        if (sprite) {
          sprite.spriteFrame = this.activeRedPocketFrame
        }
      }
      if (activeIndex >= 7) {
        activeIndex = 0
      } else {
        activeIndex++
      }
    }, 100)

    setTimeout(() => {
      clearInterval(this.timerInterval)
      this.lotteryBtn.getComponent(Sprite).spriteFrame =
        this.lotteryBtnSpriteFrame
      this.inLottery = false
      this.lottery()
    }, 4000)
  }

  animationSleep(sleepTime: number) {
    return new Promise((resolve, reject) => {
      this.scheduleOnce(() => {
        resolve(true)
      }, sleepTime)
    })
  }

  captcha() {
    return new Promise((resolve, reject) => {
      try {
        const captcha = new TencentCaptcha(
          "193586037",
          (res) => {
            resolve(res)
          },
          {
            bizState: "todo",
            ready: () => {
              console.log("ready")
            },
          }
        )
        captcha.show()
      } catch (e) {
        console.error(e)
        reject(e)
      }
    })
  }

  lottery() {
    this.hideDialog()
    this.getPrize()
  }

  getPrize() {
    const params: any = {
      game_log_id: store.gameLogId,
      score: store.score,
    }
    if (this.ticket && this.randstr) {
      params.ticket = this.ticket
      params.rand_str = this.randstr
    }

    lottery(params)
      .then((res) => {
        if (res.code == 200) {
          const { win_amount, status } = res.result
          if (win_amount && win_amount > 0) {
            EventManager.Instance.emit(EventEnum.Win, {
              win_amount: win_amount,
            })
            this.transferPrize()
          } else {
            EventManager.Instance.emit(EventEnum.NoWin)
          }
        } else {
          EventManager.Instance.emit(EventEnum.NoWin)
        }
      })
      .catch((e) => {
        Toast.show("网络繁忙，请稍后再试", 3)
        console.error(e)
        EventManager.Instance.emit(EventEnum.NoWin)
      })
  }

  transferPrize() {
    transfer({
      game_log_id: store.gameLogId,
    })
      .then((res) => {
        if (res.code == 200) {
          console.log("transfer success")
        } else {
          Toast.show(res.message, 3)
        }
      })
      .catch(() => {
        Toast.show("网络繁忙，请稍后再试", 3)
        console.log("transfer fail !")
      })
  }

  hideDialog() {
    super.hideDialog()
  }
}
