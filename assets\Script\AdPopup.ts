import {
  _decorator,
  Sprite,
  <PERSON><PERSON>rite<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ImageAsset,
  Texture2D,
  assetManager,
  sys,
  director,
} from "cc"
import { DEBUG } from "cc/env"

const { ccclass, property } = _decorator
import { Dialog } from "./Dialog"
import store from "./utils/store"
import { EventEnum } from "./utils/Enum"
import EventManager from "./utils/EventManager"
import {
  getRelativeHeight,
  getRelativeWidth,
} from "db://assets/Script/utils/Helper"

@ccclass("AdPopup")
export class AdPopup extends Dialog {
  @property(Button) knowMoreBtn: Button
  @property(Button) closeBtn: Button

  private adPopupBgUrl: string = null

  fromLottery: boolean

  start() {
    super.start()

    this.closeBtn.node.on(Button.EventType.CLICK, () => {
      console.log("close")
      this.hideDialog()
    })

    this.knowMoreBtn.node.on(Button.EventType.CLICK, () => {
      this.hideDial<PERSON>()
      EventManager.Instance.emit(EventEnum.GoAdLink, {
        fromLottery: this.fromLottery,
      })
    })
  }

  update(deltaTime: number) {
    super.update(deltaTime)
  }

  setFromLottery(fromLottery: boolean) {
    this.fromLottery = fromLottery
  }

  showDialog(x: number = 0, y: number = 0) {
    super.showDialog(x, y, false)

    let rootId = "GameDiv"

    if (DEBUG) {
      rootId = "content"
    }

    const scale = store.availWidth / 750
    const popupDesWidth = 600
    const popupDesHeight = 928
    const popupWidth = popupDesWidth * scale
    const popupHeight = popupDesHeight * scale
    const screenWidth = store.availWidth
    const screenHeight = store.availHeight
    const knowMoreBtnWidth = 170 * scale
    const knowMoreBtnHeight = 56 * scale

    console.log("screenWidth", screenWidth)
    console.log("screenHeight", screenHeight)
    console.log("popupWidth", popupWidth)
    console.log("popupHeight", popupHeight)

    const launchBtn = document.getElementById("launch-btn")
    if (launchBtn) {
      const knowMoreBtn = document.getElementById("launch-btn")

      knowMoreBtn.style.width = knowMoreBtnWidth + "px"
      knowMoreBtn.style.height = knowMoreBtnHeight + "px"
      knowMoreBtn.style.right =
        (screenWidth - popupWidth) / 2 + 28 * scale + "px"
      knowMoreBtn.style.bottom =
        (screenHeight - popupHeight) / 2 + (42 + 50) * scale + "px"

      knowMoreBtn.style.display = "block"
    }
  }

  hideDialog() {
    const knowMoreBtn = document.getElementById("launch-btn")
    if (knowMoreBtn) {
      knowMoreBtn.style.display = "none"
    }
    super.hideDialog()
  }
}
