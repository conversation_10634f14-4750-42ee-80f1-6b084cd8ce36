import { _decorator, Component, Node, Button, Event } from "cc"

const { ccclass, property } = _decorator
import { Dialog } from "./Dialog"
import EventManager from "./utils/EventManager"
import { EventEnum } from "./utils/Enum"

@ccclass("SuccessAlert")
export class SuccessAlert extends Dialog {
  @property(Button) goLotteryBtn: Button
  @property(Button) tryAgainBtn: Button

  start() {
    super.start()

    this.goLotteryBtn.node.on(Button.EventType.CLICK, () => {
      this.hideDialog()
      EventManager.Instance.emit(EventEnum.GoLottery)
    })

    this.tryAgainBtn.node.on(Button.EventType.CLICK, () => {
      this.hideDialog()
      EventManager.Instance.emit(EventEnum.TryAgain)
    })
  }

  update(deltaTime: number) {
    super.update(deltaTime)
  }
}
