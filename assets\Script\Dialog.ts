import {
  _decorator,
  Component,
  Node,
  tween,
  v3,
  view,
  Vec3,
  Graphics,
  Color,
  UITransform,
  Event,
  EventTouch,
} from "cc"

const { ccclass, property } = _decorator

@ccclass("Dialog")
export class Dialog extends Component {
  @property closeOnTouchMask: boolean = true
  @property animationDuration: number = 0.3
  @property isMask: boolean = true

  start() {
    let graphics: Graphics
    if (this.isMask) {
      graphics = this.node.addComponent(Graphics)
      let visibleSize = view.getVisibleSize()
      this.node.getComponent(UITransform).contentSize = visibleSize
      graphics.rect(
        -visibleSize.width / 2,
        -visibleSize.height / 2,
        visibleSize.width,
        visibleSize.height
      )
      graphics.fillColor = new Color(0, 0, 0, 180)
      graphics.fill()
    }

    const dialogContent = this.node.getChildByName("DialogContent")
    dialogContent.on(Node.EventType.TOUCH_START, (e: EventTouch) => {
      e.propagationStopped = true
    })

    this.node.active = false
    if (this.isMask && this.closeOnTouchMask) {
      graphics.node.on(Node.EventType.TOUCH_START, (e: EventTouch) => {
        e.propagationStopped = true
        console.log("touch mask==================")
        if (this.node.active) {
          this.hideDialog()
        }
      })
    }
  }

  public showDialog(x: number = 0, y: number = 0, animation: boolean = true) {
    console.log("show dialog")
    const dialogContent = this.node?.getChildByName("DialogContent")
    console.log(dialogContent, "dialogContent")
    this.node.setSiblingIndex(999)
    this.node.active = true
    if (dialogContent) {
      if (animation) {
        dialogContent.setScale(new Vec3(0, 0, 0))
        tween(dialogContent)
          .to(
            this.animationDuration,
            { scale: new Vec3(1, 1, 1) },
            {
              easing: "backOut",
            }
          )
          .start()
      } else {
        dialogContent.setScale(new Vec3(1, 1, 1))
      }
      this.node.setPosition(new Vec3(x, y, 0))
    } else {
      console.log("dialogContent is null")
    }
  }

  public hideDialog() {
    const dialogContent = this.node.getChildByName("DialogContent")
    this.node.dispatchEvent(new Event("close"))
    tween(dialogContent)
      .to(
        this.animationDuration,
        { scale: new Vec3(0, 0, 0) },
        {
          easing: "backIn",
          onComplete: () => {
            this.node.active = false
          },
        }
      )
      .start()
  }

  update(deltaTime: number) {}
}
