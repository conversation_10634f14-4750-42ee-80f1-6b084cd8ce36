import {
  _decorator,
  Component,
  Sprite,
  PolygonCollider2D,
  Vec2,
  Node,
  UITransform,
  EventTouch,
  Vec3,
  Camera,
  game,
} from "cc"

const { ccclass, property } = _decorator

@ccclass("Item")
export class Item extends Component {
  @property(Sprite)
  sprite: Sprite = null
  
  // 标记是否在处理点击事件中，防止重复处理
  private isProcessingTouch = false;

  onLoad() {
    // 确保有碰撞器组件
    if (!this.node.getComponent(PolygonCollider2D)) {
      this.node.addComponent(PolygonCollider2D);
    }
  }

  start() {
    // 只在冒泡阶段处理事件
    this.node.on(
      Node.EventType.TOUCH_END,
      this.onItemTouched,
      this
    );

    // 找到碰撞器，如果没有自定义的碰撞点，创建一个基础矩形碰撞形状
    const collider = this.node.getComponent(PolygonCollider2D);
    if (collider && collider.points.length === 0) {
      const uiTransform = this.node.getComponent(UITransform);
      if (uiTransform) {
        const w = uiTransform.width / 2;
        const h = uiTransform.height / 2;
        collider.points = [
          new Vec2(-w, -h),
          new Vec2(w, -h),
          new Vec2(w, h),
          new Vec2(-w, h)
        ];
        collider.apply();
      }
    }
  }

  onItemTouched(event: EventTouch) {
    // 防止事件重复处理
    if (this.isProcessingTouch) {
      return;
    }
    
    this.isProcessingTouch = true;
    
    // 获取触摸点在世界坐标系中的位置
    const touchPos = event.getUILocation();
    
    // 将世界坐标转换为节点本地坐标
    const uiTransform = this.node.getComponent(UITransform);
    if (!uiTransform) {
      this.isProcessingTouch = false;
      return;
    }
    
    const localPos = uiTransform.convertToNodeSpaceAR(new Vec3(touchPos.x, touchPos.y, 0));
    const localPos2D = new Vec2(localPos.x, localPos.y);
    
    // 获取碰撞器形状
    const collider = this.node.getComponent(PolygonCollider2D);
    if (!collider) {
      this.isProcessingTouch = false;
      return;
    }
    
    // 判断点击是否在碰撞多边形区域内
    const isInPolygon = this.isPointInPolygon(collider.points, localPos2D);
    
    if (isInPolygon) {
      // 点击在多边形区域内，触发点击事件
      console.log("点击到Item的有效区域", this.node.name);
      this.node.emit("itemClick");
      
      // 阻止事件继续传播到其他监听器
      event.propagationStopped = true;
    } else {
      // 点击在多边形区域外，手动将事件传递给下层节点
      console.log("点击在Item的透明区域，尝试传递给下层节点", this.node.name);
      
      // 尝试将点击事件传递给同一层级中在我后面渲染的节点（即视觉上在我下面的节点）
      this.passEventToNodeBelow(event, touchPos);
    }
    
    // 重置处理标志
    this.isProcessingTouch = false;
  }

  // 将事件传递给下层节点
  passEventToNodeBelow(event: EventTouch, touchPos: Vec2) {
    // 获取父节点
    const parent = this.node.parent;
    if (!parent) return;

    // 获取所有兄弟节点
    const siblings = parent.children;
    const myIndex = siblings.indexOf(this.node);
    
    // 在渲染顺序中，索引较小的节点会在上层显示
    // 所以我们需要从myIndex-1开始往下找，这些节点视觉上在当前节点下面
    for (let i = myIndex - 1; i >= 0; i--) {
      const lowerNode = siblings[i];
      
      // 检查点击位置是否在该节点的矩形范围内
      const nodeTransform = lowerNode.getComponent(UITransform);
      if (!nodeTransform) continue;
      
      // 将点击坐标转换为节点的本地坐标
      const localPos = new Vec3();
      nodeTransform.convertToNodeSpaceAR(new Vec3(touchPos.x, touchPos.y, 0), localPos);
      
      // 检查点是否在节点的矩形范围内
      const halfWidth = nodeTransform.width / 2;
      const halfHeight = nodeTransform.height / 2;
      
      if (localPos.x >= -halfWidth && localPos.x <= halfWidth && 
          localPos.y >= -halfHeight && localPos.y <= halfHeight) {
        
        // 检查该节点是否有Item组件
        const lowerItem = lowerNode.getComponent(Item);
        if (lowerItem) {
          // 检查点击是否在该Item的多边形区域内
          const collider = lowerNode.getComponent(PolygonCollider2D);
          if (collider) {
            const localPos2D = new Vec2(localPos.x, localPos.y);
            const isInPolygon = this.isPointInPolygon(collider.points, localPos2D);
            
            if (isInPolygon) {
              console.log("成功将点击传递给下层节点:", lowerNode.name);
              // 触发下层节点的点击事件
              lowerNode.emit("itemClick");
              return;
            }
          }
        }
      }
    }
    
    console.log("没有找到可接收点击的下层节点");
  }

  isPointInPolygon(points: Vec2[], point: Vec2): boolean {
    const x = point.x
    const y = point.y
    let inside = false
    for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
      const xi = points[i].x
      const yi = points[i].y
      const xj = points[j].x
      const yj = points[j].y
      const intersect =
        yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi
      if (intersect) inside = !inside
    }
    return inside
  }
}
