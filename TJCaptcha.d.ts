interface TencentCaptchaOptions {
  bizState?: any
  enableDarkMode?: boolean | string
  sdkOpts?: Object
  ready?: Function
  needFeedBack?: boolean | string
  loading?: boolean
  userLanguage?: string
  aidEncrypted?: string
  showFn?: Function
}

interface CallbackRes {
  ret: number
  ticket: string
  appid: string
  bizState?: any
  randstr?: string
  errorCode?: number
  errorMessage?: string
  verifyDuration?: number
  actionDuration?: number
  sid?: string
}

declare class TencentCaptcha {
  constructor(
    CaptchaAppId: string,
    callback: (res: CallbackRes) => void,
    options: TencentCaptchaOptions
  )

  show(): void
  destroy(): void
  getTicket(): {
    CaptchaAppId: string
    ticket: string
  }
}
