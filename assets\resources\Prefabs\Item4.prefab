[{"__type__": "cc.Prefab", "_name": "Item4", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item4", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 166}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "55cf766c-08a9-432a-a221-e6bc3d53b2c7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -13, "y": 83}, {"__type__": "cc.Vec2", "x": -41, "y": 79}, {"__type__": "cc.Vec2", "x": -60, "y": 73}, {"__type__": "cc.Vec2", "x": -69, "y": 68}, {"__type__": "cc.Vec2", "x": -81, "y": 52}, {"__type__": "cc.Vec2", "x": -88, "y": 28}, {"__type__": "cc.Vec2", "x": -91, "y": 6}, {"__type__": "cc.Vec2", "x": -91, "y": -20}, {"__type__": "cc.Vec2", "x": -89, "y": -36}, {"__type__": "cc.Vec2", "x": -81, "y": -63}, {"__type__": "cc.Vec2", "x": -74, "y": -69}, {"__type__": "cc.Vec2", "x": -64, "y": -74}, {"__type__": "cc.Vec2", "x": -30, "y": -82}, {"__type__": "cc.Vec2", "x": 23, "y": -83}, {"__type__": "cc.Vec2", "x": 50, "y": -79}, {"__type__": "cc.Vec2", "x": 67, "y": -74}, {"__type__": "cc.Vec2", "x": 77, "y": -69}, {"__type__": "cc.Vec2", "x": 83, "y": -64}, {"__type__": "cc.Vec2", "x": 87, "y": -54}, {"__type__": "cc.Vec2", "x": 91, "y": -22}, {"__type__": "cc.Vec2", "x": 90, "y": 27}, {"__type__": "cc.Vec2", "x": 86, "y": 47}, {"__type__": "cc.Vec2", "x": 82, "y": 55}, {"__type__": "cc.Vec2", "x": 78, "y": 59}, {"__type__": "cc.Vec2", "x": 77, "y": 63}, {"__type__": "cc.Vec2", "x": 72, "y": 68}, {"__type__": "cc.Vec2", "x": 68, "y": 69}, {"__type__": "cc.Vec2", "x": 63, "y": 73}, {"__type__": "cc.Vec2", "x": 44, "y": 79}, {"__type__": "cc.Vec2", "x": 15, "y": 83}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdnIqU5HVK+pcclnOANdO5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]