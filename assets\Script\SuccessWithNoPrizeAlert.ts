import {_decorator, Component, Node, Button, Event, Layout} from "cc"
import store from "./utils/store"

const {ccclass, property} = _decorator
import {Dialog} from "./Dialog"
import EventManager from "./utils/EventManager"
import {EventEnum} from "./utils/Enum"

@ccclass("SuccessWithNoPrizeAlert")
export class SuccessWithNoPrizeAlert extends Dialog {
    @property(Button) knowMoreBtn: Button
    @property(Button) tryAgainBtn: Button

    start() {
        super.start()

        const {open_ad} = store.config

        if (open_ad) {
            this.knowMoreBtn.node.active = true
            this.knowMoreBtn.node.on(Button.EventType.CLICK, () => {
                EventManager.Instance.emit(EventEnum.KnowMore)
            })
        } else {
            this.node.getComponentInChildren(Layout).paddingLeft = 242
            this.knowMoreBtn.node.active = false
        }

        this.tryAgainBtn.node.on(Button.EventType.CLICK, () => {
            this.hideDialog()
            EventManager.Instance.emit(EventEnum.TryAgain)
        })
    }

    update(deltaTime: number) {
        super.update(deltaTime)
    }
}
