[{"__type__": "cc.Prefab", "_name": "Item1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 119}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4e49335a-1a03-4a5b-a335-6e71c521ae33@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -6, "y": 59.5}, {"__type__": "cc.Vec2", "x": -18, "y": 53.5}, {"__type__": "cc.Vec2", "x": -26, "y": 45.5}, {"__type__": "cc.Vec2", "x": -26, "y": 43.5}, {"__type__": "cc.Vec2", "x": -30, "y": 40.5}, {"__type__": "cc.Vec2", "x": -30, "y": 38.5}, {"__type__": "cc.Vec2", "x": -38, "y": 28.5}, {"__type__": "cc.Vec2", "x": -40, "y": 22.5}, {"__type__": "cc.Vec2", "x": -42, "y": 21.5}, {"__type__": "cc.Vec2", "x": -53, "y": -0.5}, {"__type__": "cc.Vec2", "x": -53, "y": -3.5}, {"__type__": "cc.Vec2", "x": -56, "y": -7.5}, {"__type__": "cc.Vec2", "x": -56, "y": -10.5}, {"__type__": "cc.Vec2", "x": -63, "y": -25.5}, {"__type__": "cc.Vec2", "x": -64, "y": -39.5}, {"__type__": "cc.Vec2", "x": -62, "y": -44.5}, {"__type__": "cc.Vec2", "x": -56, "y": -50.5}, {"__type__": "cc.Vec2", "x": -49, "y": -54.5}, {"__type__": "cc.Vec2", "x": -33, "y": -58.5}, {"__type__": "cc.Vec2", "x": 34, "y": -59.5}, {"__type__": "cc.Vec2", "x": 49, "y": -56.5}, {"__type__": "cc.Vec2", "x": 57, "y": -52.5}, {"__type__": "cc.Vec2", "x": 64, "y": -42.5}, {"__type__": "cc.Vec2", "x": 64, "y": -30.5}, {"__type__": "cc.Vec2", "x": 61, "y": -20.5}, {"__type__": "cc.Vec2", "x": 52, "y": -1.5}, {"__type__": "cc.Vec2", "x": 50, "y": -0.5}, {"__type__": "cc.Vec2", "x": 48, "y": 5.5}, {"__type__": "cc.Vec2", "x": 44, "y": 9.5}, {"__type__": "cc.Vec2", "x": 39, "y": 19.5}, {"__type__": "cc.Vec2", "x": 36, "y": 21.5}, {"__type__": "cc.Vec2", "x": 36, "y": 23.5}, {"__type__": "cc.Vec2", "x": 28, "y": 32.5}, {"__type__": "cc.Vec2", "x": 28, "y": 34.5}, {"__type__": "cc.Vec2", "x": 23, "y": 38.5}, {"__type__": "cc.Vec2", "x": 23, "y": 40.5}, {"__type__": "cc.Vec2", "x": 11, "y": 52.5}, {"__type__": "cc.Vec2", "x": 9, "y": 52.5}, {"__type__": "cc.Vec2", "x": 2, "y": 58.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fQqbdYV9HvZ8WmVpC5hFE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]