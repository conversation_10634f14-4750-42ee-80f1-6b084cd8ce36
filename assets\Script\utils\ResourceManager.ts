import { _decorator, resources, Asset, Prefab } from "cc"
import Singleton from "./Singleton"

export class ResourceManager extends Singleton {
  static get Instance() {
    return super.GetInstance<ResourceManager>()
  }

  loadRes<T extends Asset>(path: string, type: new (...args: any[]) => T) {
    return new Promise<T>((resolve, reject) => {
      resources.load(path, type, (err, res) => {
        if (err) {
          reject(err)
          return
        }
        resolve(res)
      })
    })
  }

  loadDir<T extends Asset>(path: string, type: new (...args: any[]) => T) {
    return new Promise<T[]>((resolve, reject) => {
      resources.loadDir(path, type, (err, res) => {
        if (err) {
          reject(err)
          return
        }
        resolve(res)
      })
    })
  }

  /**
   * 加载预制体
   */
  async loadAllPrefabRes() {
    const criticalResources = [
      // 预加载关键预制体
      "Prefabs/SuccessAlert",
      "Prefabs/FailAlert",
      "Prefabs/LotteryAlert",
      "Prefabs/AdAlert",
      "Prefabs/WinningAlert",
      "Prefabs/DidntWinAlert",
      "Prefabs/SuccessWithNoPrizeAlert",
    ]

    const loadPromises = criticalResources.map((path) =>
      this.loadRes(path, Prefab).catch((err) => {
        console.warn(`Failed to load ${path}:`, err)
        return null
      })
    )

    await Promise.all(loadPromises)
  }
}
